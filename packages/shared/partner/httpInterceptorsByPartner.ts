import Launcher from '@xhs/launcher'

import redSellerPermission, { RED_SELLER_ID } from './redSellerPermission'
import { isPartner } from '../config/platform.config'

// 计划来源
export const enum LifeSourceFromStatus {
  pcShop = 'PC_SHOP', // 商家端
  pcPartner = 'PC_PARTNER', // 代运营商
}

export default function httpInterceptorsRequest(withSourceFrom) {
  Launcher.http.interceptors.request.use(config => {
    if (isPartner) {
      // 代运营商重写请求
      const { sellerId } = redSellerPermission.get()
      const redApiReg = /((\/api\/redlife)|(\/api\/edith\/life_service)|(\/api\/edith\/cooperation)|(\/api\/edith\/butterfly\/data)|(\/api\/edith\/material_center))/
      if (config.url && redApiReg.test(config.url)) {
        config.headers = {
          ...config.headers,
          [RED_SELLER_ID]: sellerId,
        }
        config.url = config.url.replace(redApiReg, '/api/life$1')
      }
    }
    if (withSourceFrom && !config.params?.sourceFrom) {
      config.params = {
        ...config.params,
        sourceFrom: isPartner ? LifeSourceFromStatus.pcPartner : LifeSourceFromStatus.pcShop,
      }
    }
    return config
  })
}
