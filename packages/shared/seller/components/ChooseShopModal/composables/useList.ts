import { ref } from 'vue'
import { useStore } from 'vuex'
import { toast2 as toast } from '@xhs/delight'
import {
  query_poi_list, IGetLocalLifePoiList, getDownloadBindPoiListUrl, PoiItem, getLocallifePoilistForgroup, getLocalLifePoiList,
  query_poi_list_offline_scan
} from 'shared/seller/services/area'
import { BusinessEnum, BusinessBannerEnum } from '../type'

export const useList = (disabled, sellerId) => {
  const listSource = ref<Partial<PoiItem&{key:string}>[]>([]) // 列表数据
  const bannerTitle = ref<string>(BusinessBannerEnum.COMMON)
  const loading = ref(false) // 列表加载标识
  const filterParam = ref<IGetLocalLifePoiList & {total: number}>({ // 搜索参数
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  if (sellerId) {
    filterParam.value.sellerId = sellerId
  }

  const fetchList = async (businessEnum:BusinessEnum = BusinessEnum.COMMON, isResetPageNum:boolean = false) => {
    loading.value = true
    if (isResetPageNum) {
      filterParam.value.pageNum = 1
    }
    const payload: any = {
      ...filterParam.value,
      total: undefined
    }
    const servicesMap = {
      [BusinessEnum.ACCOUNT]: query_poi_list,
      [BusinessEnum.COMMON]: getLocallifePoilistForgroup,
      [BusinessEnum.OFFLINE_SCAN]: query_poi_list_offline_scan
    }
    try {
      let queryRes
      if (sellerId) {
        bannerTitle.value = '仅展示已认领的门店，可在”门店“模块查看门店情况'
        queryRes = await getLocalLifePoiList(payload)
        listSource.value = queryRes?.shopList?.map(poiItem => ({ ...poiItem, key: poiItem.shopId })) || []
      } else {
        bannerTitle.value = BusinessBannerEnum[businessEnum || BusinessEnum.COMMON]
        // 线下扫码 查询门店特殊逻辑
        if (businessEnum === BusinessEnum.OFFLINE_SCAN) {
          payload.pageNo = payload.pageNum
          payload.poiInfo = payload.shopName
          payload.claimStatus = 'CLAIM_SUCCEEDED'
          delete payload.pageNum
          delete payload.shopName
        }
        queryRes = await (servicesMap[businessEnum] || getLocallifePoilistForgroup)(payload)
        listSource.value = (queryRes?.shopList || queryRes?.poiList || queryRes?.poiClaimInfoList)?.map(poiItem => ({ ...poiItem, key: poiItem.shopId || poiItem.poiId })) || []
      }
      filterParam.value.total = queryRes?.total || queryRes?.count || 0
    } catch (e:any) {
      console.error(e)
      toast.warning(e.message || '网络异常')
    }
    loading.value = false
  }

  return {
    listSource,
    loading,
    bannerTitle,
    filterParam,
    fetchList,
  }
}

export const useDownLoadUrl = () => {
  const store = useStore()
  const downLoadUrl = ref('')
  const sellerId = store.state?.Auth?.userInfo?.sellerId

  const fetchUrl = async () => {
    const { url } = await getDownloadBindPoiListUrl({ sellerId })
    downLoadUrl.value = url
  }
  fetchUrl()

  return {
    downLoadUrl
  }
}
