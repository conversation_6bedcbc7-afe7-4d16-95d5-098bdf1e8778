<template>
  <Banner
    class="sapcing-top"
    :closeable="false"
    :title="bannerTitle"
  />
  <!-- :title="sellerId ? BusinessBannerEnum[BusinessEnum.COMMON] : BusinessBannerEnum[props.businessEnum || BusinessEnum.COMMON]" -->
  <OutlineFilter
    v-model="filterParam"
    class="sapcing-top"
    :config="filterConfig"
  />
  <Space
    class="ultra-list-content"
    v-bind="spaceProps"
  >
    <Table
      :selected="selectedList"
      size="large"
      :columns="tableColumns"
      :data-source="listSource"
      :row-selection="rowSelection"
      :loading="loading"
      @selectedChange="changeSelected"
    />
    <Pagination
      v-model="filterParam.pageNum"
      v-model:pageSize="filterParam.pageSize"
      :total="filterParam.total"
      style="margin-top: 24px"
      @change="fetchList(props.businessEnum)"
    />
  </Space>
  <Teleport v-if="visible && active" to="#area-modal-footer">
    <Button style="min-width: 88px; margin-right: 20px;" @click="emit('changeShowStatus', false)">取消</Button>
    <Button style="min-width: 88px;" type="primary" :loading="submitLoading" :disabled="!selectedList.length || disabled" @click="confirm">提交</Button>
  </Teleport>
</template>

<script lang="tsx" setup>
  import { ref, watch } from 'vue'
  import {
    Space, Banner, Table, Pagination, Button
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import { PoiItem } from 'shared/seller/services/area'
  import {
    spaceProps, useFilter, useCitySelectFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'
  import useDelayVisible from './composables/useDelayVisible'
  import { BusinessEnum } from './type'
  import useGetSellerId from './composables/useGetSellerId'

  // 底部延迟渲染，所以teleport不立即出现
  const { visible } = useDelayVisible()
  const submitLoading = ref(false)
  const props = withDefaults(
    defineProps<{
      active: boolean
      modelValue?: string[]
      poiGroupName?: string
      poiGroupId?: string
      businessEnum?: BusinessEnum
      disabled?: boolean
      disabledIds?: string[]
      selectableRegion?: string[]
      productCreate?: boolean
    }>(),
    {
      modelValue: () => [],
    }
  )

  const { sellerId } = useGetSellerId()

  const {
    bannerTitle,
    listSource, // 列表数据
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
  } = useList(props.disabled, props.productCreate ? sellerId : '')

  const rowSelection = { // 每行 Checkbox 的属性
    getCheckboxProps: (v:PoiItem) => {
      const isDisabled = () => {
        const isDisabledById = props.disabledIds?.includes(v?.shopId || v?.poiId)
        const isDisabledByRegion = !!props.selectableRegion?.length && !!v.districtName && !props.selectableRegion.some(t => {
          const target = t.split('-').pop() as string
          return ([v.provinceName, v.cityName, v.districtName]?.some(item => item?.includes(target)))
        })
        const isDisabledByLicenseClosedMsg = !!v?.licenseClosedMsg // 门店限制文案

        return props.disabled || isDisabledById || isDisabledByRegion || isDisabledByLicenseClosedMsg
      }
      return {
        selectable: true,
        // disabled控制：不可选ids控制、可选区域控制
        disabled: isDisabled(),
      // checked: Boolean(poiGroupId) && Boolean(v?.groupId) && poiGroupId === v?.groupId,
      }
    },
    onSelect: v => v
  }

  const handleFilter = () => {
    fetchList(props.businessEnum)
  }

  const { filterConfig } = props.productCreate ? useCitySelectFilter(handleFilter) : useFilter(handleFilter)
  const { tableColumns } = useTableColumns()

  fetchList(props.businessEnum)

  const selectedList = ref<(string|number)[]>([...props.modelValue])
  watch(
    () => props.modelValue,
    () => {
      selectedList.value = [...props.modelValue]
    },
  )

  const emit = defineEmits(['update:modelValue', 'changeShowStatus', 'refresh', 'confirm', 'update:selectedList'])

  const changeSelected = (selected: (string | number)[]) => {
    selectedList.value = selected
    emit('update:selectedList', selectedList.value)
  }

  const confirm = async () => {
    emit('update:modelValue', selectedList.value)
    emit('changeShowStatus', false)
    emit('confirm', selectedList.value)
  }
  </script>
<style lang="stylus" scoped>
.sapcing-top
    margin-top 10px

.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 10px
  /deep/.ultra-metarial-toolbar-wrap
    padding 0

  .d-table-wrapper
    >>> .d-space-align-center
            align-item center
</style>
