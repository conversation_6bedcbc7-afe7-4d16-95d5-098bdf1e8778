import { http } from '@xhs/launcher'
import qs from 'query-string'
import { getPoiClaimInfoPage, IGetPoiClaimInfoPagePayload } from 'lifeactivity/src/services/edith_get_poi_claim_info_page'

const { get, post } = http

interface IUnbindPoigroup {
  poiGroupId?: string
  unbindPoiIdList?: string[]
  unbindShopIdList?: string[]
  // sellerId?: string
}

export interface ICreatePoigroup extends IUnbindPoigroup {
  poiGroupName: string
  bindShopIdList?: string[]
  poiGroupType: number
}

export interface IPoigroupList {
  // sellerId?: string
  poiGroupName?: string
  pageParam: {
    pageNum: number
    pageSize: number
  }
  total?: number
}

export type PoiGroupItem = {
  poiGroupId?: string
  poiGroupName?: string
  poiNum?: number
  createTime?: string
  qualCommitStatus?: string
  qualCommitStatusText?: string
  poiGroupType?: string
  poiGroupTypeText?: string
}

export interface RPoigroupList {
  poiGroupList?: PoiGroupItem[]
  total?: number
}

export interface ILocallifePoilistForgroup {
  // sellerId?: string
  cityName?: string
  shopName?: string
  pageNum?: number
  pageSize?: number
}

export type PoiItem = {
  shopId: string
  shopName?: string
  addressDetail?: string
  groupId?: string
  groupType?: string
  groupName?: string
  cityName?: string
  districtName?: string
  provinceName?: string
  shortAddressName?: string
  licenseClosedMsg?: string
  regionCode?: string
}

interface RLocallifePoilistForgroup {
  total?: number
  count?: number
  shopList?: PoiItem[]
}

export interface IBindedPoiList {
  // sellerId?: string
  poiGroupId?: string
  pageNum?: number
  pageSize?: number
  total?: number
}

// interface RBindedPoiListForGroup {
//   sellerId?: string
//   poiGroupId?: string
//   pageNum?: number
//   pageSize?: number
// }

interface IUploadBindPoiList {
  url: string
  poiGroupName: string
  poiGroupId?: string
  // sellerId: string
}

export interface RUploadBindPoiList {
  successNum: number
  failNum: number
  poiGroupId?: string
  failResultList: {
    shopId: string
    shopName: string
    errorMsg: string
  }[]
}

export interface ILocalLifePoiCityList {
  sellerId?: string
}

export interface RLocalLifePoiCityList {
  cityList?: string[]
}

export interface IGetLocalLifePoiList {
  sellerId?: string
  cityList?: string[]
  shopName?: string
  pageNum?: number
  pageSize?: number
}

interface LocalLifePoi {
  shopId?: string
  shopName?: string
  addressDetail?: string
  provinceName?: string
  cityName?: string
  districtName: string
}

export interface RGetLocalLifePoiList {
  shopList?: LocalLifePoi[]
  total?: number
  count?: number
}

// 新建区域
export const postCreatePoigroup = (payload: ICreatePoigroup) => post<{ poiGroupId: string }>('CREATE_POIGROUP_INFO', payload, { transform: false })

// 区域绑定门店批量上传
export const postUploadBindPoiList = async (payload: IUploadBindPoiList) => post<RUploadBindPoiList>('POST_IMPORT_POI_GROUP_BIND_POI_LIST', payload, { transform: false })

// 区域门店搜索
export const getLocallifePoilistForgroup = (params: ILocallifePoilistForgroup) => get<RLocallifePoilistForgroup>('GET_LOCALLIFE_POILIST_FORGROUP', { params }, { transform: false })

// 商家认领门店所在城市列表
export const getLocalLifePoiCityList = (params: ILocalLifePoiCityList) => get<RLocalLifePoiCityList>('GET_POI_CITY_LIST', { params }, { transform: false })

// 商家门店搜索
export const getLocalLifePoiList = (params: IGetLocalLifePoiList) => get<RGetLocalLifePoiList>('GET_LOCAL_LIFE_POI_LIST', { params, paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' }) }, { transform: false })

// 门店列表下载
export const getDownloadBindPoiListUrl = (params: { sellerId?: string }) => get<{ url: string }>('GET_DOWNLOAD_BIND_POI_LIST', { params }, { transform: false })

// 新建子账号，选择门店，门店列表接口
export const query_poi_list = (params: ILocallifePoilistForgroup) => get<RLocallifePoilistForgroup>('QUERY_POI_LIST', { params }, { transform: false })

export const query_poi_list_offline_scan = (params: IGetPoiClaimInfoPagePayload) => getPoiClaimInfoPage(params, { transform: false })
