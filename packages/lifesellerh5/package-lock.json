{"name": "lifesellerh5", "version": "1.2.10", "lockfileVersion": 1, "requires": true, "dependencies": {"@ampproject/remapping": {"version": "2.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "requires": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}}, "@antv/adjust": {"version": "0.2.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/adjust/download/@antv/adjust-0.2.5.tgz", "integrity": "sha1-uze7SgqHyj9LZghIvJrAfwK89ds=", "requires": {"@antv/util": "~2.0.0", "tslib": "^1.10.0"}}, "@antv/event-emitter": {"version": "0.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/event-emitter/download/@antv/event-emitter-0.1.3.tgz", "integrity": "sha1-PgYyO53NVaMkHdx8VFjPq9IJUWQ="}, "@antv/f2": {"version": "4.0.51", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/f2/-/f2-4.0.51.tgz", "integrity": "sha512-BqrU2oMmfJE47tzRDmQRFgXj8kk0hf6IsWoa+IURZ2aPtZNO0OABpOtNndFO2uFJDgwi9Jg8D5mqpdWeMeiMQg==", "requires": {"@antv/adjust": "~0.2.5", "@antv/event-emitter": "^0.1.2", "@antv/f2-graphic": "^0.0.16", "@antv/scale": "~0.3.3", "@antv/util": "~2.0.6", "@babel/runtime": "^7.12.5", "d3-cloud": "~1.2.5"}}, "@antv/f2-graphic": {"version": "0.0.16", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/f2-graphic/-/f2-graphic-0.0.16.tgz", "integrity": "sha512-oZ+yHvkffY7w1dECcs1BEsYSRms4plFby9KnN2fGosSg/VLj1fZtHipnHT3C+Qj+Avo7VSl8GmAWBdpIGuZV8g==", "requires": {"@antv/util": "~2.0.6", "@babel/runtime": "^7.12.5"}}, "@antv/scale": {"version": "0.3.18", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/scale/download/@antv/scale-0.3.18.tgz", "integrity": "sha1-uRH0MbPguVR7amX2bQ0/opW17zI=", "requires": {"@antv/util": "~2.0.3", "fecha": "~4.2.0", "tslib": "^2.0.0"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8="}}}, "@antv/util": {"version": "2.0.17", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antv/util/download/@antv/util-2.0.17.tgz", "integrity": "sha1-6O9CrKeJKBWyKSafPdEMazx1l6k=", "requires": {"csstype": "^3.0.8", "tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8="}}}, "@babel/code-frame": {"version": "7.26.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.26.2.tgz", "integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==", "requires": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "dependencies": {"picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/compat-data": {"version": "7.26.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.26.8.tgz", "integrity": "sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ=="}, "@babel/core": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/core/-/core-7.26.9.tgz", "integrity": "sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==", "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/helper-compilation-targets": "^7.26.5", "@babel/helper-module-transforms": "^7.26.0", "@babel/helpers": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}}, "@babel/generator": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.26.9.tgz", "integrity": "sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==", "requires": {"@babel/parser": "^7.26.9", "@babel/types": "^7.26.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-annotate-as-pure": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz", "integrity": "sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==", "requires": {"@babel/types": "^7.27.3"}, "dependencies": {"@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}}}, "@babel/helper-compilation-targets": {"version": "7.26.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz", "integrity": "sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==", "requires": {"@babel/compat-data": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "dependencies": {"browserslist": {"version": "4.24.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.24.4.tgz", "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==", "requires": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}}}}, "@babel/helper-create-class-features-plugin": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "integrity": "sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "integrity": "sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "dependencies": {"jsesc": {"version": "3.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsesc/download/jsesc-3.0.2.tgz", "integrity": "sha1-u4sJpll7pCZCXy5KByRcPQC5ND4="}, "regexpu-core": {"version": "6.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regexpu-core/-/regexpu-core-6.2.0.tgz", "integrity": "sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==", "requires": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}}, "regjsgen": {"version": "0.8.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regjsgen/-/regjsgen-0.8.0.tgz", "integrity": "sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q=="}, "regjsparser": {"version": "0.12.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regjsparser/-/regjsparser-0.12.0.tgz", "integrity": "sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==", "requires": {"jsesc": "~3.0.2"}}}}, "@babel/helper-define-polyfill-provider": {"version": "0.6.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "integrity": "sha512-jljfR1rGnXXNWnmQg2K3+bvhkxB51Rl32QRaOTuwwjviGrHzIbSc8+x9CpraDtbT7mfyjXObULP4w/adunNwAw==", "requires": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}}, "@babel/helper-member-expression-to-functions": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz", "integrity": "sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/helper-module-imports": {"version": "7.25.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==", "requires": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}}, "@babel/helper-module-transforms": {"version": "7.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==", "requires": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}}, "@babel/helper-optimise-call-expression": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz", "integrity": "sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==", "requires": {"@babel/types": "^7.27.1"}, "dependencies": {"@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}}}, "@babel/helper-plugin-utils": {"version": "7.26.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "integrity": "sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg=="}, "@babel/helper-remap-async-to-generator": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "integrity": "sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/helper-replace-supers": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz", "integrity": "sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==", "requires": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "integrity": "sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/helper-string-parser": {"version": "7.25.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA=="}, "@babel/helper-validator-identifier": {"version": "7.25.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ=="}, "@babel/helper-validator-option": {"version": "7.25.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw=="}, "@babel/helper-wrap-function": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz", "integrity": "sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==", "requires": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/helpers": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helpers/-/helpers-7.26.9.tgz", "integrity": "sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==", "requires": {"@babel/template": "^7.26.9", "@babel/types": "^7.26.9"}}, "@babel/parser": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.26.9.tgz", "integrity": "sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==", "requires": {"@babel/types": "^7.26.9"}}, "@babel/plugin-bugfix-firefox-class-in-computed-class-key": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz", "integrity": "sha512-QPG3C9cCVRQLxAVwmefEmwdTanECuUBMQZ/ym5kiw3XKCGA7qkuQLcjWWHcrD/GKbn/WmJwaezfuuAOcyKlRPA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-bugfix-safari-class-field-initializer-scope": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz", "integrity": "sha512-qNeq3bCKnGgLkEXUuFry6dPlGfCdQNZbn7yUAPCInwAJHMU7THJfrBSozkcWq5sNM6RcF3S8XyQL2A52KNR9IA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz", "integrity": "sha512-g4L7OYun04N1WyqMNjldFwlfPCLVkgB54A/YCXICZYBsvJJE3kByKv9c9+R/nAfmIfjl2rKYLNyMHboYbZaWaA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz", "integrity": "sha512-oO02gcONcD5O1iTLi/6frMJBIwWEHceWGSGqrpCmEL8nogiS6J9PBlE48CaK20/Jx1LuRml9aDftLgdjXT8+Cw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz", "integrity": "sha512-6BpaYGDavZqkI6yT+KSPdpZFfpnd68UKXbcjI9pJ13pvHhPrCKWOOLp+ysvMeA+DxnhuPpgIaRpxRxo5A9t5jw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-proposal-export-namespace-from": {"version": "7.18.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.18.9.tgz", "integrity": "sha1-X3MTqzSM2xnVkBRfkkdUDpR2EgM=", "requires": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}}, "@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "integrity": "sha1-eET5KJVG76n+usLeTP41igUL1wM="}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "requires": {"@babel/helper-plugin-utils": "^7.12.13"}}, "@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=", "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz", "integrity": "sha1-AolkqbqA28CUyRXEh618TnpmRlo=", "requires": {"@babel/helper-plugin-utils": "^7.8.3"}}, "@babel/plugin-syntax-import-assertions": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz", "integrity": "sha512-UT/Jrhw57xg4ILHLFnzFpPDlMbcdEicaAtjPQpbj9wa8T4r5KVWCimHcL/460g8Ht0DMxDyjsLgiWSkVjnwPFg==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "integrity": "sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-jsx": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "integrity": "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=", "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-typescript": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "integrity": "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "integrity": "sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz", "integrity": "sha512-8Z4TGic6xW70FKThA5HYEKKyBpOOsucTOD1DjU3fZxDg+K3zBJcXMFnt/4yQiZnf5+MiOMSXQ9PaEK/Ilh1DeA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-async-generator-functions": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.27.1.tgz", "integrity": "sha512-eST9RrwlpaoJBDHShc+DS2SG4ATTi2MYNb4OxYkf3n+7eb49LWpnS+HSpVfW4x927qQwgk8A2hGNVaajAEw0EA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz", "integrity": "sha512-NREkZsZVJS4xmTr8qzE5y8AfIPqsdQfRuUiLRTEzb7Qii8iFWCyDKaUV2c0rCuh4ljDZ98ALHP/PetiBV2nddA==", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.27.1.tgz", "integrity": "sha512-cnqkuOtZLapWYZUYM5rVIdv1nXYuFVIltZ6ZJ7nIj585QsjKM5dhL2Fu/lICXZ1OyIAFc7Qy+bvDAtTXqGrlhg==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-block-scoping": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.5.tgz", "integrity": "sha512-JF6uE2s67f0y2RZcm2kpAUEbD50vH62TyWVebxwHAlbSdM49VqPz8t4a1uIjp4NIOIZ4xzLfjY5emt/RCyC7TQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-class-properties": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz", "integrity": "sha512-D0VcalChDMtuRvJIu3U/fwWjf8ZMykz5iZsg77Nuj821vCKI3zCyRLwRdWbsuJ/uRwZhZ002QtCqIkwC/ZkvbA==", "requires": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-class-static-block": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.27.1.tgz", "integrity": "sha512-s734HmYU78MVzZ++joYM+NkJusItbdRcbm+AGRgJCt3iA+yux0QpD9cBVdz3tKyrjVYWRl7j0mHSmv4lhV0aoA==", "requires": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-classes": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz", "integrity": "sha512-7iLhfFAubmpeJe/Wo2TVuDrykh/zlWXLzPNdL0Jqn/Xu8R3QQ8h9ff8FQoISZOsw74/HFqFI7NX63HN7QFIHKA==", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.27.1", "globals": "^11.1.0"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/compat-data": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg=="}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "browserslist": {"version": "4.25.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "requires": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "caniuse-lite": {"version": "1.0.30001724", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001724.tgz", "integrity": "sha512-WqJo7p0TbHDOythNTqYujmaJTvtYRZrjpP8TCvH6Vb9CYJerJNKamKzIWOM4BkQatWj9H2lYulpdAQNBe7QhNA=="}, "electron-to-chromium": {"version": "1.5.171", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.171.tgz", "integrity": "sha512-scWpzXEJEMrGJa4Y6m/tVotb0WuvNmasv3wWVzUAeCgKU0ToFOhUW6Z+xWnRQANMYGxN4ngJXIThgBJOqzVPCQ=="}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-computed-properties": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz", "integrity": "sha512-lj9PGWvMTVksbWiDT2tW68zGS/cyo4AkZ/QTp0sQT0mjPopCmrSkzxeXkznjqBxzDI6TclZhOJbBmbBLjuOZUw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-destructuring": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.3.tgz", "integrity": "sha512-s4Jrok82JpiaIprtY2nHsYmrThKvvwgHwjgd7UMiYhZaN0asdXNLr0y+NjTfkA7SyQE5i2Fb7eawUOZmLvyqOA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-dotall-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz", "integrity": "sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.27.1.tgz", "integrity": "sha512-MTyJk98sHvSs+cvZ4nOauwTTG1JeonDjSGvGGUNHreGQns+Mpt6WX/dVzWBHgg+dYZhkC4X+zTDfkTU+Vy9y7Q==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-duplicate-named-capturing-groups-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz", "integrity": "sha512-hkGcueTEzuhB30B3eJCbCYeCaaEQOmQR0AdvzpD4LoN0GXMWzzGSuRrxR2xTnCrvNbVwK9N6/jQ92GSLfiZWoQ==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-dynamic-import": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.27.1.tgz", "integrity": "sha512-MHzkWQcEmjzzVW9j2q8LGjwGWpG2mjwaaB0BNQwst3FIjqsg8Ct/mIZlvSPJvfi9y2AC8mi/ktxbFVL9pZ1I4A==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.27.1.tgz", "integrity": "sha512-uspvXnhHvGKf2r4VVtBpeFnuDWsJLQ6MF6lGJLC89jBR1uoVeqM416AZtTuhTezOfgHicpJQmoD5YUakO/YmXQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz", "integrity": "sha512-tQvHWSZ3/jH2xuq/vZDy0jNn+ZdXJeM8gHvX4lnJmsc3+50yPlWdZXIc5ay+umX+2/tJIqHqiEqcJvxlmIvRvQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-for-of": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz", "integrity": "sha512-BfbWFFEJFQzLCQ5N8VocnCtA8J1CLkNTe2Ms2wocj75dd6VpiqS5Z5quTYcUoo4Yq+DN0rtikODccuv7RU81sw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-function-name": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz", "integrity": "sha512-1bQeydJF9Nr1eBCMMbC+hdwmRlsv5XYOMu03YSWFwNs0HsAmtSxxF1fyuYPqemVldVyFmlCU7w8UE14LupUSZQ==", "requires": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/compat-data": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg=="}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "browserslist": {"version": "4.25.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "requires": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "caniuse-lite": {"version": "1.0.30001724", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001724.tgz", "integrity": "sha512-WqJo7p0TbHDOythNTqYujmaJTvtYRZrjpP8TCvH6Vb9CYJerJNKamKzIWOM4BkQatWj9H2lYulpdAQNBe7QhNA=="}, "electron-to-chromium": {"version": "1.5.171", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.171.tgz", "integrity": "sha512-scWpzXEJEMrGJa4Y6m/tVotb0WuvNmasv3wWVzUAeCgKU0ToFOhUW6Z+xWnRQANMYGxN4ngJXIThgBJOqzVPCQ=="}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-json-strings": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.27.1.tgz", "integrity": "sha512-6WVLVJiTjqcQauBhn1LkICsR2H+zm62I3h9faTDKt1qP4jn2o72tSvqMwtGFKGTpojce0gJs+76eZ2uCHRZh0Q==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-literals": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz", "integrity": "sha512-0HCFSepIpLTkLcsi86GG3mTUzxV5jpmbv97hTETW3yzrAij8aqlD36toB1D0daVFJM8NK6GvKO0gslVQmm+zZA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz", "integrity": "sha512-SJvDs5dXxiae4FbSL1aBJlG4wvl594N6YEVVn9e3JGulwioy6z3oPjx/sQBO3Y4NwUu5HNix6KJ3wBZoewcdbw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz", "integrity": "sha512-hqoBX4dcZ1I33jCSWcXrP+1Ku7kdqXf1oeah7ooKOIiAdKQ+uqftgCFNOSzA5AMS2XIHEYeGFg4cKRCdpxzVOQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-modules-amd": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz", "integrity": "sha512-iCsytMg/N9/oFq6n+gFTvUYDZQOMK5kEdeYxmxt91fcJGycfxVP9CnrxoliM0oumFERba2i8ZtwRUCMhvP1LnA==", "requires": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz", "integrity": "sha512-OJguuwlTYlN0gBZFRPqwOGNWssZjfIUdS7HMYtN8c1KmwpwHFBwTeFZrg9XZa+DFTitWOW5iTAG7tyCUPsCCyw==", "requires": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.27.1.tgz", "integrity": "sha512-w5N1XzsRbc0PQStASMksmUeqECuzKuTJer7kFagK8AXgpCMkeDMO5S+aaFb7A51ZYDF7XI34qsTX+fkHiIm5yA==", "requires": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-modules-umd": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.27.1.tgz", "integrity": "sha512-iQBE/xC5BV1OxJbp6WG7jq9IWiD+xxlZhLrdwpPkTX3ydmXdvoCpyfJN7acaIBZaOqTfr76pgzqBJflNbeRK+w==", "requires": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/generator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "requires": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}}, "@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="}, "@babel/parser": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "requires": {"@babel/types": "^7.27.3"}}, "@babel/template": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.27.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.27.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "integrity": "sha512-SstR5JYy8ddZvD6MhV0tM/j16Qds4mIpJTOd1Yu9J9pJjH93bxHECF7pgtc28XvkzTD6Pxcm/0Z73Hvk7kb3Ng==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-new-target": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz", "integrity": "sha512-f6PiYeqXQ05lYq3TIfIDu/MtliKUbNwkGApPUvyo6+tc7uaR4cPjPe7DFPr15Uyycg2lZU6btZ575CuQoYh7MQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "integrity": "sha512-aGZh6xMo6q9vq1JGcw58lZ1Z0+i0xB2x0XaauNIUXd6O1xXc3RwoWEBlsTQrY4KQ9Jf0s5rgD6SiNkaUdJegTA==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz", "integrity": "sha512-fdPKAcujuvEChxDBJ5c+0BTaS6revLV7CJL08e4m3de8qJfNIuCc2nc7XJYOjBoTMJeqSmwXJ0ypE14RCjLwaw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-object-rest-spread": {"version": "7.27.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.3.tgz", "integrity": "sha512-7ZZtznF9g4l2JCImCo5LNKFHB5eXnN39lLtLY5Tg+VkR0jwOt7TBciMckuiQIOIW7L5tkQOCh3bVGYeXgMx52Q==", "requires": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.3", "@babel/plugin-transform-parameters": "^7.27.1"}, "dependencies": {"@babel/compat-data": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg=="}, "@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="}, "browserslist": {"version": "4.25.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "requires": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "caniuse-lite": {"version": "1.0.30001724", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001724.tgz", "integrity": "sha512-WqJo7p0TbHDOythNTqYujmaJTvtYRZrjpP8TCvH6Vb9CYJerJNKamKzIWOM4BkQatWj9H2lYulpdAQNBe7QhNA=="}, "electron-to-chromium": {"version": "1.5.171", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.171.tgz", "integrity": "sha512-scWpzXEJEMrGJa4Y6m/tVotb0WuvNmasv3wWVzUAeCgKU0ToFOhUW6Z+xWnRQANMYGxN4ngJXIThgBJOqzVPCQ=="}}}, "@babel/plugin-transform-object-super": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz", "integrity": "sha512-SFy8S9plRPbIcxlJ8A6mT/CxFdJx/c04JEctz4jf8YZaVS2px34j7NXRrlGlHkN/M2gnpL37ZpGRGVFLd3l8Ng==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz", "integrity": "sha512-txEAEKzYrHEX4xSZN4kJ+OfKXFVSWKB2ZxM9dpcE3wT7smwkNmXo5ORRlVzMVdJbD+Q8ILTgSD7959uj+3Dm3Q==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz", "integrity": "sha512-B<PERSON>mKPPIuc8EkZgNKsv0X4bPmOoayeu4F1YCwx2/CfmDSXDbp7GnzlUH+/ul5VGfRg1AoFPsrIThlEBj2xb4CAg==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-parameters": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz", "integrity": "sha512-018KRk76HWKeZ5l4oTj2zPpSh+NbGdt0st5S6x0pga6HgrjBOJb24mMDHorFopOOd6YHkLgOZ+zaCjZGPO4aKg==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-private-methods": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz", "integrity": "sha512-10FVt+X55AjRAYI9BrdISN9/AQWHqldOeZDUoLyif1Kn05a56xVBXb8ZouL8pZ9jem8QpXaOt8TS7RHUIS+GPA==", "requires": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz", "integrity": "sha512-5J+IhqTi1XPa0DXF83jYOaARrX+41gOewWbkPyjMNRDqgOCqdffGh8L3f/Ek5utaEBZExjSAzcyjmV9SSAWObQ==", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-property-literals": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz", "integrity": "sha512-oThy3BCuCha8kDZ8ZkgOg2exvPYUlprMukKQXI1r1pJ47NCvxfkEy8vK+r/hT9nF0Aa4H1WUPZZjHTFtAhGfmQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-regenerator": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.5.tgz", "integrity": "sha512-uhB8yHerfe3MWnuLAhEbeQ4afVoqv8BQsPqrTv7e/jZ9y00kJL6l9a/f4OWaKxotmjzewfEyXE1vgDJenkQ2/Q==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-regexp-modifiers": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.27.1.tgz", "integrity": "sha512-TtEciroaiODtXvLZv4rmfMhkCv8jx3wgKpL68PuiPh2M4fvz5jhsA7697N1gMvkvr/JTF13DrFYyEbY9U7cVPA==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-reserved-words": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.27.1.tgz", "integrity": "sha512-V2ABPHIJX4kC7HegLkYoDpfg9PVmuWy/i6vUM5eGK22bx4YVFD3M5F0QQnWQoDs6AGsUWTVOopBiMFQgHaSkVw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "integrity": "sha512-N/wH1vcn4oYawbJ13Y/FxcQrWk63jhfNa7jef0ih7PHSIHX2LB7GWE1rkPrOnka9kwMxb6hMl19p7lidA+EHmQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-spread": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz", "integrity": "sha512-kpb3HUqaILBJcRFVhFUs6Trdd4mkrzcGXss+6/mxUd273PfbWqSDHRzMT2234gIg2QYfAjvXLSquP1xECSg09Q==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz", "integrity": "sha512-lhInBO5bi/Kowe2/aLdBAawijx+q1pQzicSgnkB6dUPc1+RC8QmJHKf2OjvU+NZWitguJHEaEmbV6VWEouT58g==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-template-literals": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz", "integrity": "sha512-fBJKiV7F2DxZUkg5EtHKXQdbsbURW3DZKQUWphDum0uRP6eHGGa/He9mc0mypL680pb+e/lDIthRohlv8NCHkg==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.1.tgz", "integrity": "sha512-RiSILC+nRJM7FY5srIyc4/fGIwUhyDuuBSdWn4y6yT6gm652DpCHZjIipgn6B7MQ1ITOUnAKWixEUjQRIBIcLw==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-typescript": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz", "integrity": "sha512-Q5sT5+O4QUebHdbwKedFBEwRLb02zJ7r4A5Gg2hUoLuU3FjdMcyqcywqUrLCaDsFCxzokf7u9kuy7qz51YUuAg==", "requires": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-unicode-escapes": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.27.1.tgz", "integrity": "sha512-Ysg4v6AmF26k9vpfFuTZg8HRfVWzsh1kVfowA23y9j/Gu6dOuahdUVhkLqpObp3JIv27MLSii6noRnuKN8H0Mg==", "requires": {"@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-unicode-property-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.27.1.tgz", "integrity": "sha512-uW20S39PnaTImxp39O5qFlHLS9LJEmANjMG7SxIhap8rCHqu0Ik+tLEPX5DKmHn6CsWQ7j3lix2tFOa5YtL12Q==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz", "integrity": "sha512-xvINq24TRojDuyt6JGtHmkVkrfVV3FPT16uytxImLeBZqW3/H52yN+kM1MGuyPkIQxrzKwPHs5U/MP3qKyzkGw==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/plugin-transform-unicode-sets-regex": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.27.1.tgz", "integrity": "sha512-EtkOujbc4cgvb0mlpQefi4NTPBzhSIevblFevACNLUspmrALgmEBdL/XfnyyITfd8fKBZrZys92zOWcik7j9Tw==", "requires": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}}}, "@babel/preset-env": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/preset-env/-/preset-env-7.27.2.tgz", "integrity": "sha512-Ma4zSuYSlGNRlCLO+EAzLnCmJK2vdstgv+n7aUP+/IKZrOfWHOJVdSJtuub8RzHTj3ahD37k5OKJWvzf16TQyQ==", "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "^7.27.1", "@babel/plugin-bugfix-safari-class-field-initializer-scope": "^7.27.1", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.27.1", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.27.1", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "^7.27.1", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-generator-functions": "^7.27.1", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-block-scoped-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-computed-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1", "@babel/plugin-transform-dotall-regex": "^7.27.1", "@babel/plugin-transform-duplicate-keys": "^7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-dynamic-import": "^7.27.1", "@babel/plugin-transform-exponentiation-operator": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-function-name": "^7.27.1", "@babel/plugin-transform-json-strings": "^7.27.1", "@babel/plugin-transform-literals": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-member-expression-literals": "^7.27.1", "@babel/plugin-transform-modules-amd": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-modules-systemjs": "^7.27.1", "@babel/plugin-transform-modules-umd": "^7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-new-target": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.2", "@babel/plugin-transform-object-super": "^7.27.1", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-property-literals": "^7.27.1", "@babel/plugin-transform-regenerator": "^7.27.1", "@babel/plugin-transform-regexp-modifiers": "^7.27.1", "@babel/plugin-transform-reserved-words": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-spread": "^7.27.1", "@babel/plugin-transform-sticky-regex": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/plugin-transform-typeof-symbol": "^7.27.1", "@babel/plugin-transform-unicode-escapes": "^7.27.1", "@babel/plugin-transform-unicode-property-regex": "^7.27.1", "@babel/plugin-transform-unicode-regex": "^7.27.1", "@babel/plugin-transform-unicode-sets-regex": "^7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "core-js-compat": "^3.40.0", "semver": "^6.3.1"}, "dependencies": {"@babel/compat-data": {"version": "7.27.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg=="}, "@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="}, "browserslist": {"version": "4.25.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "requires": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "caniuse-lite": {"version": "1.0.30001724", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001724.tgz", "integrity": "sha512-WqJo7p0TbHDOythNTqYujmaJTvtYRZrjpP8TCvH6Vb9CYJerJNKamKzIWOM4BkQatWj9H2lYulpdAQNBe7QhNA=="}, "electron-to-chromium": {"version": "1.5.171", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.171.tgz", "integrity": "sha512-scWpzXEJEMrGJa4Y6m/tVotb0WuvNmasv3wWVzUAeCgKU0ToFOhUW6Z+xWnRQANMYGxN4ngJXIThgBJOqzVPCQ=="}}}, "@babel/preset-modules": {"version": "0.1.6-no-external-plugins", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz", "integrity": "sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==", "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}}, "@babel/preset-typescript": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz", "integrity": "sha512-l7WfQfX0WK4M0v2RudjuQK4u99BS6yLHYEmdtVPP7lKV013zr9DygFuWNlnbvQ9LR+LS0Egz/XAvGx5U9MX0fQ==", "requires": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-****************************************+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="}, "@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="}}}, "@babel/register": {"version": "7.27.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/register/-/register-7.27.1.tgz", "integrity": "sha512-K13lQpoV54LATKkzBpBAEu1GGSIRzxR9f4IN4V8DCDgiUMo2UDGagEZr3lPeVNJPLkWUi5JE4hCHKneVTwQlYQ==", "requires": {"clone-deep": "^4.0.1", "find-cache-dir": "^2.0.0", "make-dir": "^2.1.0", "pirates": "^4.0.6", "source-map-support": "^0.5.16"}, "dependencies": {"make-dir": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/make-dir/download/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "requires": {"pify": "^4.0.1", "semver": "^5.6.0"}}, "semver": {"version": "5.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg="}, "source-map-support": {"version": "0.5.21", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map-support/download/source-map-support-0.5.21.tgz", "integrity": "sha1-BP58f54e0tZiIzwoyys1ufY/bk8=", "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}}}, "@babel/runtime": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/runtime/download/@babel/runtime-7.26.9.tgz", "integrity": "sha1-qkxvrMZbnLP4fXUSX/1HeBtHVDM=", "requires": {"regenerator-runtime": "^0.14.0"}, "dependencies": {"regenerator-runtime": {"version": "0.14.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz", "integrity": "sha1-NWreECY/aF3aElEAzYYsHbiVMn8="}}}, "@babel/template": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.26.9.tgz", "integrity": "sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==", "requires": {"@babel/code-frame": "^7.26.2", "@babel/parser": "^7.26.9", "@babel/types": "^7.26.9"}}, "@babel/traverse": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.26.9.tgz", "integrity": "sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==", "requires": {"@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.9", "@babel/parser": "^7.26.9", "@babel/template": "^7.26.9", "@babel/types": "^7.26.9", "debug": "^4.3.1", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.26.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.26.9.tgz", "integrity": "sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==", "requires": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}}, "@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz", "integrity": "sha1-daLotRy3WKdVPWgEpZMteqznXDk="}, "@emnapi/core": {"version": "1.4.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emnapi/core/-/core-1.4.3.tgz", "integrity": "sha512-4m62DuCE07lw01soJwPiBGC0nAww0Q+RY70VZ+n49yDIO13yyinhbWCeNnaob0lakDtWQzSdtNWzJeOJt2ma+g==", "dev": true, "optional": true, "requires": {"@emnapi/wasi-threads": "1.0.2", "tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "dev": true, "optional": true}}}, "@emnapi/runtime": {"version": "1.4.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emnapi/runtime/-/runtime-1.4.3.tgz", "integrity": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==", "dev": true, "optional": true, "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "dev": true, "optional": true}}}, "@emnapi/wasi-threads": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emnapi/wasi-threads/-/wasi-threads-1.0.2.tgz", "integrity": "sha512-5n3nTJblwRi8LlXkJ9eBzu+kZR8Yxcc7ubakyQTFzPMtIhFpUBRbsnc2Dv88IZDIbCDlBiWrknhB4Lsz7mg6BA==", "dev": true, "optional": true, "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "dev": true, "optional": true}}}, "@emotion/babel-plugin": {"version": "11.13.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz", "integrity": "sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==", "requires": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}, "dependencies": {"convert-source-map": {"version": "1.9.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/convert-source-map/download/convert-source-map-1.9.0.tgz", "integrity": "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8="}, "escape-string-regexp": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ="}, "source-map": {"version": "0.5.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map/download/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="}}}, "@emotion/cache": {"version": "11.14.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/cache/-/cache-11.14.0.tgz", "integrity": "sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==", "requires": {"@emotion/memoize": "^0.9.0", "@emotion/sheet": "^1.4.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "stylis": "4.2.0"}}, "@emotion/css": {"version": "11.11.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/css/download/@emotion/css-11.11.2.tgz", "integrity": "sha1-5foIHQxuM1NS4bwrBZU7YYMtylo=", "requires": {"@emotion/babel-plugin": "^11.11.0", "@emotion/cache": "^11.11.0", "@emotion/serialize": "^1.1.2", "@emotion/sheet": "^1.2.2", "@emotion/utils": "^1.2.1"}}, "@emotion/hash": {"version": "0.9.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g=="}, "@emotion/memoize": {"version": "0.9.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/memoize/-/memoize-0.9.0.tgz", "integrity": "sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ=="}, "@emotion/serialize": {"version": "1.3.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/serialize/-/serialize-1.3.3.tgz", "integrity": "sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==", "requires": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}}, "@emotion/sheet": {"version": "1.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/sheet/-/sheet-1.4.0.tgz", "integrity": "sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg=="}, "@emotion/unitless": {"version": "0.10.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/unitless/-/unitless-0.10.0.tgz", "integrity": "sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg=="}, "@emotion/utils": {"version": "1.4.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/utils/-/utils-1.4.2.tgz", "integrity": "sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA=="}, "@emotion/weak-memoize": {"version": "0.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz", "integrity": "sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg=="}, "@esbuild/aix-ppc64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/aix-ppc64/-/aix-ppc64-0.23.0.tgz", "integrity": "sha512-3sG8Zwa5fMcA9bgqB8AfWPQ+HFke6uD3h1s3RIwUNK8EG7a4buxvuFTs3j1IMs2NXAk9F30C/FF4vxRgQCcmoQ==", "dev": true, "optional": true}, "@esbuild/android-arm": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/android-arm/-/android-arm-0.23.0.tgz", "integrity": "sha512-+KuOHTKKyIKgEEqKbGTK8W7mPp+hKinbMBeEnNzjJGyFcWsfrXjSTNluJHCY1RqhxFurdD8uNXQDei7qDlR6+g==", "dev": true, "optional": true}, "@esbuild/android-arm64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/android-arm64/-/android-arm64-0.23.0.tgz", "integrity": "sha512-EuHFUYkAVfU4qBdyivULuu03FhJO4IJN9PGuABGrFy4vUuzk91P2d+npxHcFdpUnfYKy0PuV+n6bKIpHOB3prQ==", "dev": true, "optional": true}, "@esbuild/android-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/android-x64/-/android-x64-0.23.0.tgz", "integrity": "sha512-WRrmKidLoKDl56LsbBMhzTTBxrsVwTKdNbKDalbEZr0tcsBgCLbEtoNthOW6PX942YiYq8HzEnb4yWQMLQuipQ==", "dev": true, "optional": true}, "@esbuild/darwin-arm64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/darwin-arm64/-/darwin-arm64-0.23.0.tgz", "integrity": "sha512-YLntie/IdS31H54Ogdn+v50NuoWF5BDkEUFpiOChVa9UnKpftgwzZRrI4J132ETIi+D8n6xh9IviFV3eXdxfow==", "dev": true, "optional": true}, "@esbuild/darwin-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/darwin-x64/-/darwin-x64-0.23.0.tgz", "integrity": "sha512-IMQ6eme4AfznElesHUPDZ+teuGwoRmVuuixu7sv92ZkdQcPbsNHzutd+rAfaBKo8YK3IrBEi9SLLKWJdEvJniQ==", "dev": true, "optional": true}, "@esbuild/freebsd-arm64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/freebsd-arm64/-/freebsd-arm64-0.23.0.tgz", "integrity": "sha512-0muYWCng5vqaxobq6LB3YNtevDFSAZGlgtLoAc81PjUfiFz36n4KMpwhtAd4he8ToSI3TGyuhyx5xmiWNYZFyw==", "dev": true, "optional": true}, "@esbuild/freebsd-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/freebsd-x64/-/freebsd-x64-0.23.0.tgz", "integrity": "sha512-XKDVu8IsD0/q3foBzsXGt/KjD/yTKBCIwOHE1XwiXmrRwrX6Hbnd5Eqn/WvDekddK21tfszBSrE/WMaZh+1buQ==", "dev": true, "optional": true}, "@esbuild/linux-arm": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-arm/-/linux-arm-0.23.0.tgz", "integrity": "sha512-SEELSTEtOFu5LPykzA395Mc+54RMg1EUgXP+iw2SJ72+ooMwVsgfuwXo5Fn0wXNgWZsTVHwY2cg4Vi/bOD88qw==", "dev": true, "optional": true}, "@esbuild/linux-arm64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-arm64/-/linux-arm64-0.23.0.tgz", "integrity": "sha512-j1t5iG8jE7BhonbsEg5d9qOYcVZv/Rv6tghaXM/Ug9xahM0nX/H2gfu6X6z11QRTMT6+aywOMA8TDkhPo8aCGw==", "dev": true, "optional": true}, "@esbuild/linux-ia32": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-ia32/-/linux-ia32-0.23.0.tgz", "integrity": "sha512-P7O5Tkh2NbgIm2R6x1zGJJsnacDzTFcRWZyTTMgFdVit6E98LTxO+v8LCCLWRvPrjdzXHx9FEOA8oAZPyApWUA==", "dev": true, "optional": true}, "@esbuild/linux-loong64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-loong64/-/linux-loong64-0.23.0.tgz", "integrity": "sha512-InQwepswq6urikQiIC/kkx412fqUZudBO4SYKu0N+tGhXRWUqAx+Q+341tFV6QdBifpjYgUndV1hhMq3WeJi7A==", "dev": true, "optional": true}, "@esbuild/linux-mips64el": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-mips64el/-/linux-mips64el-0.23.0.tgz", "integrity": "sha512-J9rflLtqdYrxHv2FqXE2i1ELgNjT+JFURt/uDMoPQLcjWQA5wDKgQA4t/dTqGa88ZVECKaD0TctwsUfHbVoi4w==", "dev": true, "optional": true}, "@esbuild/linux-ppc64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-ppc64/-/linux-ppc64-0.23.0.tgz", "integrity": "sha512-cShCXtEOVc5GxU0fM+dsFD10qZ5UpcQ8AM22bYj0u/yaAykWnqXJDpd77ublcX6vdDsWLuweeuSNZk4yUxZwtw==", "dev": true, "optional": true}, "@esbuild/linux-riscv64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-riscv64/-/linux-riscv64-0.23.0.tgz", "integrity": "sha512-HEtaN7Y5UB4tZPeQmgz/UhzoEyYftbMXrBCUjINGjh3uil+rB/QzzpMshz3cNUxqXN7Vr93zzVtpIDL99t9aRw==", "dev": true, "optional": true}, "@esbuild/linux-s390x": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-s390x/-/linux-s390x-0.23.0.tgz", "integrity": "sha512-WDi3+NVAuyjg/Wxi+o5KPqRbZY0QhI9TjrEEm+8dmpY9Xir8+HE/HNx2JoLckhKbFopW0RdO2D72w8trZOV+Wg==", "dev": true, "optional": true}, "@esbuild/linux-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-x64/-/linux-x64-0.23.0.tgz", "integrity": "sha512-a3pMQhUEJkITgAw6e0bWA+F+vFtCciMjW/LPtoj99MhVt+Mfb6bbL9hu2wmTZgNd994qTAEw+U/r6k3qHWWaOQ==", "dev": true, "optional": true}, "@esbuild/netbsd-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/netbsd-x64/-/netbsd-x64-0.23.0.tgz", "integrity": "sha512-cRK+YDem7lFTs2Q5nEv/HHc4LnrfBCbH5+JHu6wm2eP+d8OZNoSMYgPZJq78vqQ9g+9+nMuIsAO7skzphRXHyw==", "dev": true, "optional": true}, "@esbuild/openbsd-arm64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/openbsd-arm64/-/openbsd-arm64-0.23.0.tgz", "integrity": "sha512-suXjq53gERueVWu0OKxzWqk7NxiUWSUlrxoZK7usiF50C6ipColGR5qie2496iKGYNLhDZkPxBI3erbnYkU0rQ==", "dev": true, "optional": true}, "@esbuild/openbsd-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/openbsd-x64/-/openbsd-x64-0.23.0.tgz", "integrity": "sha512-6p3nHpby0DM/v15IFKMjAaayFhqnXV52aEmv1whZHX56pdkK+MEaLoQWj+H42ssFarP1PcomVhbsR4pkz09qBg==", "dev": true, "optional": true}, "@esbuild/sunos-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/sunos-x64/-/sunos-x64-0.23.0.tgz", "integrity": "sha512-BFelBGfrBwk6LVrmFzCq1u1dZbG4zy/Kp93w2+y83Q5UGYF1d8sCzeLI9NXjKyujjBBniQa8R8PzLFAUrSM9OA==", "dev": true, "optional": true}, "@esbuild/win32-arm64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/win32-arm64/-/win32-arm64-0.23.0.tgz", "integrity": "sha512-lY6AC8p4Cnb7xYHuIxQ6iYPe6MfO2CC43XXKo9nBXDb35krYt7KGhQnOkRGar5psxYkircpCqfbNDB4uJbS2jQ==", "dev": true, "optional": true}, "@esbuild/win32-ia32": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/win32-ia32/-/win32-ia32-0.23.0.tgz", "integrity": "sha512-7L1bHlOTcO4ByvI7OXVI5pNN6HSu6pUQq9yodga8izeuB1KcT2UkHaH6118QJwopExPn0rMHIseCTx1CRo/uNA==", "dev": true, "optional": true}, "@esbuild/win32-x64": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/win32-x64/-/win32-x64-0.23.0.tgz", "integrity": "sha512-Arm+WgUFLUATuoxCJcahGuk6Yj9Pzxd6l11Zb/2aAuv5kWWvvfhLFo2fni4uSK5vzlUdCGZ/BdV5tH8klj8p8g==", "dev": true, "optional": true}, "@floating-ui/core": {"version": "1.7.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/core/-/core-1.7.1.tgz", "integrity": "sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==", "requires": {"@floating-ui/utils": "^0.2.9"}}, "@floating-ui/dom": {"version": "1.7.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/dom/-/dom-1.7.1.tgz", "integrity": "sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==", "requires": {"@floating-ui/core": "^1.7.1", "@floating-ui/utils": "^0.2.9"}}, "@floating-ui/utils": {"version": "0.2.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/utils/-/utils-0.2.9.tgz", "integrity": "sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg=="}, "@floating-ui/vue": {"version": "1.1.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/vue/-/vue-1.1.6.tgz", "integrity": "sha512-XFlUzGHGv12zbgHNk5FN2mUB7ROul3oG2ENdTpWdE+qMFxyNxWSRmsoyhiEnpmabNm6WnUvR1OvJfUfN4ojC1A==", "requires": {"@floating-ui/dom": "^1.0.0", "@floating-ui/utils": "^0.2.9", "vue-demi": ">=0.13.0"}}, "@icon-park/svg": {"version": "1.4.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@icon-park/svg/-/svg-1.4.2.tgz", "integrity": "sha512-1X0DA+1e0R0liYvw+Nb2BQmF1oEo/wS3o/JYkQYifPJXCGYij2vN9sJf/NNhbzDsJWTg4W2bbzZjJvC7Q4w4oQ=="}, "@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz", "integrity": "sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=", "requires": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}}, "@istanbuljs/schema": {"version": "0.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz", "integrity": "sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg="}, "@jest/console": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/console/-/console-29.7.0.tgz", "integrity": "sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==", "requires": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "@jest/core": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/core/-/core-29.7.0.tgz", "integrity": "sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==", "requires": {"@jest/console": "^29.7.0", "@jest/reporters": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.7.0", "jest-config": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "@jest/environment": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/environment/-/environment-29.7.0.tgz", "integrity": "sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==", "requires": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}}, "@jest/expect": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/expect/-/expect-29.7.0.tgz", "integrity": "sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==", "requires": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}}, "@jest/expect-utils": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "integrity": "sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==", "requires": {"jest-get-type": "^29.6.3"}}, "@jest/fake-timers": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "integrity": "sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==", "requires": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}}, "@jest/globals": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/globals/-/globals-29.7.0.tgz", "integrity": "sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==", "requires": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/types": "^29.6.3", "jest-mock": "^29.7.0"}}, "@jest/reporters": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/reporters/-/reporters-29.7.0.tgz", "integrity": "sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==", "requires": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "@jest/schemas": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/schemas/-/schemas-29.6.3.tgz", "integrity": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==", "requires": {"@sinclair/typebox": "^0.27.8"}}, "@jest/source-map": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/source-map/-/source-map-29.6.3.tgz", "integrity": "sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==", "requires": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}}, "@jest/test-result": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/test-result/-/test-result-29.7.0.tgz", "integrity": "sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==", "requires": {"@jest/console": "^29.7.0", "@jest/types": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}}, "@jest/test-sequencer": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "integrity": "sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==", "requires": {"@jest/test-result": "^29.7.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "slash": "^3.0.0"}}, "@jest/transform": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/transform/-/transform-29.7.0.tgz", "integrity": "sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==", "requires": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "@jest/types": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/types/-/types-29.6.3.tgz", "integrity": "sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==", "requires": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz", "integrity": "sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=", "requires": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}}, "@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y="}, "@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz", "integrity": "sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA="}, "@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz", "integrity": "sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo="}, "@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz", "integrity": "sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=", "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@napi-rs/wasm-runtime": {"version": "0.2.11", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.11.tgz", "integrity": "sha512-9DPkXtvHydrcOsopiYpUgPHpmj0HWZKMUnL2dZqpvC42lsratuBG06V5ipyno0fUek5VlFsNQ+AcFATSrJXgMA==", "dev": true, "optional": true, "requires": {"@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0"}}, "@oxc-resolver/binding-darwin-arm64": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-darwin-arm64/-/binding-darwin-arm64-6.0.0.tgz", "integrity": "sha512-GKsfwUPgo4CjJioksA+DVEILT0aWhrbTBKHTiEvkTNC+bsafttSm0xqrIutCQqfqwuSa+Uj0VHylmL3Vv0F/7g==", "dev": true, "optional": true}, "@oxc-resolver/binding-darwin-x64": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-darwin-x64/-/binding-darwin-x64-6.0.0.tgz", "integrity": "sha512-hwKfm4aT4SLuTkdF2NDYqYEnE9+m4emXLfFZ7D1mTIRul8If/fJop4I4YuIDrJfHVLQmSkpbPbI16XrNK3TftA==", "dev": true, "optional": true}, "@oxc-resolver/binding-freebsd-x64": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-freebsd-x64/-/binding-freebsd-x64-6.0.0.tgz", "integrity": "sha512-ZxFpS90awfLxWW0JqWFWO71p73SGWKhuocOMNQV30MtKZx5fX4lemnNl92Lr6Hvqg4egeSsPO5SGZbnMD5YShw==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-arm-gnueabihf": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-arm-gnueabihf/-/binding-linux-arm-gnueabihf-6.0.0.tgz", "integrity": "sha512-ztc09+LDBxbAfndqTSvzz4KqN2fRRDCjj1eDRBGZMF5zQu/ThasERwh1ZzRp3sGZGRroZLQRCJunstS5OJKpww==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-arm64-gnu": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-arm64-gnu/-/binding-linux-arm64-gnu-6.0.0.tgz", "integrity": "sha512-+x1xrEm2G/aOlTMzH3p53ayEEOCTFh4+H5EazdA1ljJP8m/ztrhtZGAo95dclYrCsRNP6KuVmIpw0Y4/RZT7EQ==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-arm64-musl": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-arm64-musl/-/binding-linux-arm64-musl-6.0.0.tgz", "integrity": "sha512-jgo0lz1569+yGpcZCjh0/wzgbvekTAaOB5JaOOWtgh7jvuVDIo6+m884Pf9V5U3Z2VLZts4J+e8hA1urA9Y1lg==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-riscv64-gnu": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-riscv64-gnu/-/binding-linux-riscv64-gnu-6.0.0.tgz", "integrity": "sha512-uEhw/2oSnBp5PNv6sBev1koH4thSy1eH8LA6N3gAklrznqhFNqqvmXjlKZm9ek3bVFG44Hlx9BS5/tT0hXPbqA==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-s390x-gnu": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-s390x-gnu/-/binding-linux-s390x-gnu-6.0.0.tgz", "integrity": "sha512-QR8d1f58XyTlkbATYxo2XhqyBNVT/Ma+z5dDvmjyYMa2S9u5yHKOch10I9fx/kLjRqfHzpl2H32NwwBnkaTzzg==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-x64-gnu": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-x64-gnu/-/binding-linux-x64-gnu-6.0.0.tgz", "integrity": "sha512-CBp1yw8/jBhMuJnye1DJNUx1Rvpw4Zur4QqtjXXa+0kzTXr4qSsEsrdZj2p4USBQX/ComtK4UVPX4FqDj6VR0Q==", "dev": true, "optional": true}, "@oxc-resolver/binding-linux-x64-musl": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-linux-x64-musl/-/binding-linux-x64-musl-6.0.0.tgz", "integrity": "sha512-FM3bdl0ZfjGnHsFLUSPny9H8nsFXYXEVaD5juOnBW+RIcxN6tS9atzmki5ZmeTqgyDLZ68pM//b/UlI4V0GGvA==", "dev": true, "optional": true}, "@oxc-resolver/binding-wasm32-wasi": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-wasm32-wasi/-/binding-wasm32-wasi-6.0.0.tgz", "integrity": "sha512-FLk/ip9wCbbeqBJAXCGmmZCMDNa9wT/Kbw1m5xWcMYy88Z65/zuAQs7Gg/okm77X/DE1ZJ766bnC3Cmz6SmWaA==", "dev": true, "optional": true, "requires": {"@napi-rs/wasm-runtime": "^0.2.9"}}, "@oxc-resolver/binding-win32-arm64-msvc": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-win32-arm64-msvc/-/binding-win32-arm64-msvc-6.0.0.tgz", "integrity": "sha512-WEF2dSpwF5MEN1Zt/+dCCWpWXxsZTPPZPJXARV/1SP0ul9N0oijYyWO+8WYE0qREU8B0Toh/YGkA/wLSui3eRg==", "dev": true, "optional": true}, "@oxc-resolver/binding-win32-x64-msvc": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@oxc-resolver/binding-win32-x64-msvc/-/binding-win32-x64-msvc-6.0.0.tgz", "integrity": "sha512-eTn8RUr6D2C+BGPG0ECtsqvUo8B+HvkhTkBG0Jel/7DqU+WCTNOT64+Ww9ZUhQxPJKa4laR9Zyu5yo/SaF6qPQ==", "dev": true, "optional": true}, "@popperjs/core": {"version": "2.11.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@popperjs/core/download/@popperjs/core-2.11.8.tgz", "integrity": "sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8="}, "@riant/icons": {"version": "0.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@riant/icons/-/@riant/icons-0.1.2.tgz", "integrity": "sha1-t/El+J2bWc4Zf5uneEjBkvisJfw="}, "@riant/use": {"version": "0.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@riant/use/-/@riant/use-0.1.2.tgz", "integrity": "sha1-GyO8UK2ycuOhw9yCYU7bnlkcxPU="}, "@rspack/lite-tapable": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rspack/lite-tapable/-/lite-tapable-1.0.1.tgz", "integrity": "sha512-VynGOEsVw2s8TAlLf/uESfrgfrq2+rcXB1muPJYBWbsm1Oa6r5qVQhjA5ggM6z/coYPrsVMgovl3Ff7Q7OCp1w==", "dev": true}, "@sinclair/typebox": {"version": "0.27.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz", "integrity": "sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4="}, "@sinonjs/commons": {"version": "3.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinonjs/commons/-/commons-3.0.1.tgz", "integrity": "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==", "requires": {"type-detect": "4.0.8"}}, "@sinonjs/fake-timers": {"version": "10.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==", "requires": {"@sinonjs/commons": "^3.0.0"}}, "@swc/core": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core/-/core-1.11.7.tgz", "integrity": "sha512-ICuzjyfz8Hh3U16Mb21uCRJeJd/lUgV999GjgvPhJSISM1L8GDSB5/AMNcwuGs7gFywTKI4vAeeXWyCETUXHAg==", "dev": true, "requires": {"@swc/core-darwin-arm64": "1.11.7", "@swc/core-darwin-x64": "1.11.7", "@swc/core-linux-arm-gnueabihf": "1.11.7", "@swc/core-linux-arm64-gnu": "1.11.7", "@swc/core-linux-arm64-musl": "1.11.7", "@swc/core-linux-x64-gnu": "1.11.7", "@swc/core-linux-x64-musl": "1.11.7", "@swc/core-win32-arm64-msvc": "1.11.7", "@swc/core-win32-ia32-msvc": "1.11.7", "@swc/core-win32-x64-msvc": "1.11.7", "@swc/counter": "^0.1.3", "@swc/types": "^0.1.19"}}, "@swc/core-darwin-arm64": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-darwin-arm64/-/core-darwin-arm64-1.11.7.tgz", "integrity": "sha512-3+LhCP2H50CLI6yv/lhOtoZ5B/hi7Q/23dye1KhbSDeDprLTm/KfLJh/iQqwaHUponf5m8C2U0y6DD+HGLz8Yw==", "dev": true, "optional": true}, "@swc/core-darwin-x64": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-darwin-x64/-/core-darwin-x64-1.11.7.tgz", "integrity": "sha512-1diWpJqwX1XmOghf9ENFaeRaTtqLiqlZIW56RfOqmeZ7tPp3qS7VygWb9akptBsO5pEA5ZwNgSerD6AJlQcjAw==", "dev": true, "optional": true}, "@swc/core-linux-arm-gnueabihf": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.11.7.tgz", "integrity": "sha512-MV8+hLREf0NN23NuSKemsjFaWjl/HnqdOkE7uhXTnHzg8WTwp6ddVtU5Yriv15+d/ktfLWPVAOhLHQ4gzaoa8A==", "dev": true, "optional": true}, "@swc/core-linux-arm64-gnu": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.11.7.tgz", "integrity": "sha512-5GNs8ZjHQy/UTSnzzn+gm1RCUpCYo43lsxYOl8mpcnZSfxkNFVpjfylBv0QuJ5qhdfZ2iU55+v4iJCwCMtw0nA==", "dev": true, "optional": true}, "@swc/core-linux-arm64-musl": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.11.7.tgz", "integrity": "sha512-cTydaYBwDbVV5CspwVcCp9IevYWpGD1cF5B5KlBdjmBzxxeWyTAJRtKzn8w5/UJe/MfdAptarpqMPIs2f33YEQ==", "dev": true, "optional": true}, "@swc/core-linux-x64-gnu": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.11.7.tgz", "integrity": "sha512-YAX2KfYPlbDsnZiVMI4ZwotF3VeURUrzD+emJgFf1g26F4eEmslldgnDrKybW7V+bObsH22cDqoy6jmQZgpuPQ==", "dev": true, "optional": true}, "@swc/core-linux-x64-musl": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.11.7.tgz", "integrity": "sha512-mYT6FTDZyYx5pailc8xt6ClS2yjKmP8jNHxA9Ce3K21n5qkKilI5M2N7NShwXkd3Ksw3F29wKrg+wvEMXTRY/A==", "dev": true, "optional": true}, "@swc/core-win32-arm64-msvc": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.11.7.tgz", "integrity": "sha512-uLDQEcv0BHcepypstyxKkNsW6KfLyI5jVxTbcxka+B2UnMcFpvoR87nGt2JYW0grO2SNZPoFz+UnoKL9c6JxpA==", "dev": true, "optional": true}, "@swc/core-win32-ia32-msvc": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.11.7.tgz", "integrity": "sha512-wiq5G3fRizdxAJVFcon7zpyfbfrb+YShuTy+TqJ4Nf5PC0ueMOXmsmeuyQGApn6dVWtGCyymYQYt77wHeQajdA==", "dev": true, "optional": true}, "@swc/core-win32-x64-msvc": {"version": "1.11.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.11.7.tgz", "integrity": "sha512-/zQdqY4fHkSORxEJ2cKtRBOwglvf/8gs6Tl4Q6VMx2zFtFpIOwFQstfY5u8wBNN2Z+PkAzyUCPoi8/cQFK8HLQ==", "dev": true, "optional": true}, "@swc/counter": {"version": "0.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/counter/-/counter-0.1.3.tgz", "integrity": "sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==", "dev": true}, "@swc/types": {"version": "0.1.23", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@swc/types/-/types-0.1.23.tgz", "integrity": "sha512-u1iIVZV9Q0jxY+yM2vw/hZGDNudsN85bBpTqzAQ9rzkxW9D+e3aEM4Han+ow518gSewkXgjmEK0BD79ZcNVgPw==", "dev": true, "requires": {"@swc/counter": "^0.1.3"}}, "@tybys/wasm-util": {"version": "0.9.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@tybys/wasm-util/-/wasm-util-0.9.0.tgz", "integrity": "sha512-6+7nlbMVX/PVDCwaIQ8nTOPveOcFLSt8GcXdx8hD0bt39uWxYT88uXzqTd4fTvqta7oeUJqudepapKNt2DYJFw==", "dev": true, "optional": true, "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "dev": true, "optional": true}}}, "@types/accepts": {"version": "1.3.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/accepts/-/accepts-1.3.7.tgz", "integrity": "sha512-Pay9fq2lM2wXPWbteBsRAGiWH2hig4ZE2asK+mm7kUzlxRTfL961rj89I6zV/E3PcIkDqyuBEcMxFT7rccugeQ==", "dev": true, "requires": {"@types/node": "*"}}, "@types/babel__core": {"version": "7.20.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "requires": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.27.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==", "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.20.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==", "requires": {"@babel/types": "^7.20.7"}}, "@types/body-parser": {"version": "1.19.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/body-parser/-/body-parser-1.19.6.tgz", "integrity": "sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==", "dev": true, "requires": {"@types/connect": "*", "@types/node": "*"}}, "@types/connect": {"version": "3.4.38", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==", "dev": true, "requires": {"@types/node": "*"}}, "@types/content-disposition": {"version": "0.5.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/content-disposition/-/content-disposition-0.5.9.tgz", "integrity": "sha512-8uYXI3Gw35MhiVYhG3s295oihrxRyytcRHjSjqnqZVDDy/xcGBRny7+Xj1Wgfhv5QzRtN2hB2dVRBUX9XW3UcQ==", "dev": true}, "@types/cookies": {"version": "0.9.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/cookies/-/cookies-0.9.1.tgz", "integrity": "sha512-E/DPgzifH4sM1UMadJMWd6mO2jOd4g1Ejwzx8/uRCDpJis1IrlyQEcGAYEomtAqRYmD5ORbNXMeI9U0RiVGZbg==", "dev": true, "requires": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}}, "@types/express": {"version": "5.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/express/-/express-5.0.3.tgz", "integrity": "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==", "dev": true, "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "5.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "integrity": "sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==", "dev": true, "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "@types/graceful-fs": {"version": "4.1.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "integrity": "sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==", "requires": {"@types/node": "*"}}, "@types/http-assert": {"version": "1.5.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/http-assert/-/http-assert-1.5.6.tgz", "integrity": "sha512-TTEwmtjgVbYAzZYWyeHPrrtWnfVkm8tQkP8P21uQifPgMRgjrow3XDEYqucuC8SKZJT7pUnhU/JymvjggxO9vw==", "dev": true}, "@types/http-errors": {"version": "2.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/http-errors/-/http-errors-2.0.5.tgz", "integrity": "sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==", "dev": true}, "@types/http-proxy": {"version": "1.17.16", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/http-proxy/-/http-proxy-1.17.16.tgz", "integrity": "sha512-sdWoUajOB1cd0A8cRRQ1cfyWNbmFKLAqBB89Y8x5iYyG/mkJHc0YUH8pdWBy2omi9qtCpiIgGjuwO0dQST2l5w==", "dev": true, "requires": {"@types/node": "*"}}, "@types/istanbul-lib-coverage": {"version": "2.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w=="}, "@types/istanbul-lib-report": {"version": "3.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "integrity": "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==", "requires": {"@types/istanbul-lib-coverage": "*"}}, "@types/istanbul-reports": {"version": "3.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "integrity": "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==", "requires": {"@types/istanbul-lib-report": "*"}}, "@types/keygrip": {"version": "1.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/keygrip/-/keygrip-1.0.6.tgz", "integrity": "sha512-l<PERSON>uNAY9xeJt7Bx4t4dx0rYCDqGPW8RXhQZK1td7d4H6E9zYbLoOtjBvfwdTKpsyxQI/2jv+armjX/RW+ZNpXOQ==", "dev": true}, "@types/koa": {"version": "2.15.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa/-/koa-2.15.0.tgz", "integrity": "sha512-7QFsywoE5URbuVnG3loe03QXuGajrnotr3gQkXcEBShORai23MePfFYdhz90FEtBBpkyIYQbVD+evKtloCgX3g==", "dev": true, "requires": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}}, "@types/koa-compose": {"version": "3.2.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa-compose/-/koa-compose-3.2.8.tgz", "integrity": "sha512-4Olc63RY+MKvxMwVknCUDhRQX1pFQoBZ/lXcRLP69PQkEpze/0cr8LNqJQe5NFb/b19DWi2a5bTi2VAlQzhJuA==", "dev": true, "requires": {"@types/koa": "*"}}, "@types/lodash": {"version": "4.17.17", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/lodash/-/lodash-4.17.17.tgz", "integrity": "sha512-RRVJ+J3J+WmyOTqnz3PiBLA501eKwXl2noseKOrNo/6+XEHjTAxO4xHvxQB6QuNm+s4WRbn6rSiap8+EA+ykFQ=="}, "@types/lodash.throttle": {"version": "4.1.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/lodash.throttle/-/lodash.throttle-4.1.9.tgz", "integrity": "sha512-PCPVfpfueguWZQB7pJQK890F2scYKoDUL3iM522AptHWn7d5NQmeS/LTEHIcLr5PaTzl3dK2Z0xSUHHTHwaL5g==", "requires": {"@types/lodash": "*"}}, "@types/mime": {"version": "1.3.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==", "dev": true}, "@types/node": {"version": "24.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/-/node-24.0.3.tgz", "integrity": "sha512-R4I/kzCYAdRLzfiCabn9hxWfbuHS573x+r0dJMkkzThEa7pbrcDWK+9zu3e7aBOouf+rQAciqPFMnxwr0aWgKg==", "requires": {"undici-types": "~7.8.0"}}, "@types/parse-json": {"version": "4.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/parse-json/-/parse-json-4.0.2.tgz", "integrity": "sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw=="}, "@types/qs": {"version": "6.14.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==", "dev": true}, "@types/range-parser": {"version": "1.2.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==", "dev": true}, "@types/runes": {"version": "0.4.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/runes/-/runes-0.4.3.tgz", "integrity": "sha512-kncnfKlRj4FM0+9IRBlZ/06b1BNVDya3d5hN5kFfuzCNAgZFZuApz/XBqe0+d6Y5cV/f86UD8q2ehnaSVdtBrw=="}, "@types/send": {"version": "0.17.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/send/-/send-0.17.5.tgz", "integrity": "sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==", "dev": true, "requires": {"@types/mime": "^1", "@types/node": "*"}}, "@types/serve-static": {"version": "1.15.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/serve-static/-/serve-static-1.15.8.tgz", "integrity": "sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==", "dev": true, "requires": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "@types/stack-utils": {"version": "2.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/stack-utils/-/stack-utils-2.0.3.tgz", "integrity": "sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw=="}, "@types/url-parse": {"version": "1.4.11", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/url-parse/-/url-parse-1.4.11.tgz", "integrity": "sha512-FKvKIqRaykZtd4n47LbK/W/5fhQQ1X7cxxzG9A48h0BGN+S04NH7ervcCjM8tyR0lyGru83FAHSmw2ObgKoESg=="}, "@types/uuid": {"version": "8.3.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/uuid/download/@types/uuid-8.3.4.tgz", "integrity": "sha1-vYakNhffBZR4fTi3NfVcgFvs8bw="}, "@types/web-bluetooth": {"version": "0.0.16", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/web-bluetooth/download/@types/web-bluetooth-0.0.16.tgz", "integrity": "sha1-HRKHOo5JVnNx8qdf4+f37cpmYtg="}, "@types/yargs": {"version": "17.0.33", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/yargs/-/yargs-17.0.33.tgz", "integrity": "sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==", "requires": {"@types/yargs-parser": "*"}}, "@types/yargs-parser": {"version": "21.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "integrity": "sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ=="}, "@vant/popperjs": {"version": "1.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vant/popperjs/download/@vant/popperjs-1.3.0.tgz", "integrity": "sha1-4O/wFxJLWyNS7zs2pt8GJ39EAPI="}, "@vueuse/core": {"version": "9.13.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/core/download/@vueuse/core-9.13.0.tgz", "integrity": "sha1-L2nmbRkFweTuvCSaAXWc+I6gDPQ=", "requires": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}}, "@vueuse/metadata": {"version": "9.13.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/metadata/download/@vueuse/metadata-9.13.0.tgz", "integrity": "sha1-vCWmza0bGpPDbOMBkRJNplIFOf8="}, "@vueuse/shared": {"version": "9.13.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/shared/download/@vueuse/shared-9.13.0.tgz", "integrity": "sha1-CJ/0zE4uekAV5XqPMuSznQljU7k=", "requires": {"vue-demi": "*"}}, "@xhs/abtest": {"version": "0.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/abtest/-/@xhs/abtest-0.7.0.tgz", "integrity": "sha512-QZ6Mt1roDO+IxZbSzDel+uG7LYmdJUl6EV5pfl35KK9PMacgxFSMaY5pqehnDw4ZKXLDkBS4M2LchGEFBuEU2A==", "requires": {"@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-typescript": "^7.21.4", "@xhs/ozone-bridge": "^2.17.6", "babel-preset-env": "^1.7.0", "jest": "^29.5.0"}, "dependencies": {"@xhs/ozone-bridge": {"version": "2.37.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-2.37.0.tgz", "integrity": "sha1-PwPSe+zs1H3hdfGWy0FuUawLf0I=", "requires": {"@xhs/data-transform": "^0.3.3", "@xhs/logger": "^2.2.2", "@xhs/ozone-detector": "^3.4.4", "prop-types": "^15.7.2", "uuid": "^7.0.2"}}, "uuid": {"version": "7.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-7.0.3.tgz", "integrity": "sha1-xcnyyM8l3Ao3LE3xRBxB9b0MaAs="}}}, "@xhs/bridge-shared": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/bridge-shared/-/@xhs/bridge-shared-1.1.0.tgz", "integrity": "sha1-xEDsWkXOguJFpOTApN2m0D4WIGY="}, "@xhs/cube-style": {"version": "2.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/cube-style/-/@xhs/cube-style-2.2.3.tgz", "integrity": "sha1-hsQBf/IdQCSjmSNw/MicIYr76po=", "requires": {"normalize.css": "^8.0.1", "postcss-pxtorem": "^5.1.1"}}, "@xhs/data-transform": {"version": "0.3.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/data-transform/-/@xhs/data-transform-0.3.3.tgz", "integrity": "sha1-m8VzExVq0PY41l+jeZIVvsCOixU="}, "@xhs/delight-charts-mobile": {"version": "1.0.18", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-charts-mobile/-/@xhs/delight-charts-mobile-1.0.18.tgz", "integrity": "sha512-tvMxRtLR/FM+VXHwdNYb67UHtUQkMh7Pt7RsTgfMLUOZqLicptZ4fpxy+sLQ6uHpHAakGrTNmWk7rhmNaiIQ/Q==", "requires": {"@antv/f2": "^4.0.48", "@popperjs/core": "^2.11.8", "numerify": "^1.2.9"}}, "@xhs/eaglet": {"version": "1.1.1-1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/eaglet/-/@xhs/eaglet-1.1.1-1.tgz", "integrity": "sha1-6yLLASob3ZYQ6UitHKKjir8+THY="}, "@xhs/eaglet-emitter-base": {"version": "0.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/eaglet-emitter-base/-/@xhs/eaglet-emitter-base-0.1.3.tgz", "integrity": "sha1-uGdG8CcpCeyg9GI1Hm98ftr7Ig8="}, "@xhs/formula-babel-register": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/formula-babel-register/-/@xhs/formula-babel-register-1.0.0.tgz", "integrity": "sha1-5SUOnqlu5ckAIPJ/99Udda5JRXY=", "requires": {"@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@babel/register": "^7.6.2"}}, "@xhs/formula-plugin-swc": {"version": "4.0.23", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/formula-plugin-swc/-/@xhs/formula-plugin-swc-4.0.23.tgz", "integrity": "sha512-du93CNkkdBlg2hxD3XFVOJUFpbXxmCPP7ZEZQY7c6eozNdZ7HJ0i20VDCppxoczIiA+7TGZY8s5Vy6ZyYJW+KA==", "dev": true, "requires": {"@swc/core": "1.11.7", "@xhs/formula-shared": "4.0.23", "@xhs/formula-types": "4.0.23", "swc-loader": "0.2.6", "swc-minify-webpack-plugin": "2.1.2"}}, "@xhs/formula-shared": {"version": "4.0.23", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/formula-shared/-/@xhs/formula-shared-4.0.23.tgz", "integrity": "sha512-b+Vgmw5mBtUjrd+LR32eXLHVteVpzXKCNEs1QlQER0TxiJPraXpdTcIG6pR5rk2KYCq+9/ytiEOZ1c/vgqcYRw==", "dev": true, "requires": {"deepmerge": "4.3.1", "es-toolkit": "1.29.0", "esbuild": "0.23.0", "jiti": "2.3.3", "json-stable-stringify": "^1.1.1", "module-alias": "2.2.3", "oxc-resolver": "6.0.0", "picocolors": "1.1.1", "rslog": "1.2.3", "rspack-chain": "1.2.1"}, "dependencies": {"picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true}}}, "@xhs/formula-types": {"version": "4.0.23", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/formula-types/-/@xhs/formula-types-4.0.23.tgz", "integrity": "sha512-MySP79TOyVjO/0P6TiT4Q3vsUP8twgB1InDPdqG/x1NqAD7/TCgjKEKH5UtK8AngVQ06OsIv6lPPzVCUipJmtQ==", "dev": true, "requires": {"@types/babel__core": "7.20.5", "@types/koa": "2.15.0", "eruda": "3.4.1", "html-rspack-plugin": "6.0.2", "http-proxy-middleware": "3.0.0", "postcss": "8.4.41", "rspack-chain": "1.2.1", "webpack-merge": "6.0.1"}, "dependencies": {"picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true}, "postcss": {"version": "8.4.41", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss/-/postcss-8.4.41.tgz", "integrity": "sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==", "dev": true, "requires": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}}}}, "@xhs/google-protobuf": {"version": "0.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/google-protobuf/-/@xhs/google-protobuf-0.1.1.tgz", "integrity": "sha1-tx4S/T+3CgGUyfXzShwcq80PPB8="}, "@xhs/http": {"version": "1.15.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/http/-/@xhs/http-1.15.0.tgz", "integrity": "sha512-3Pvj2Hjx02APOJWTLvtbRYbvVxCvjYdtPDG0PPvwezqwUAmJNj+85ryMuUQfFa6QS1a03LievSA8+oesQhEh7w==", "requires": {"@xhs/data-transform": "^0.3.3", "@xhs/logger": "^3.0.1", "@xhs/untrace": "^0.0.1", "axios": "^0.19.2"}, "dependencies": {"@xhs/logger": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logger/-/@xhs/logger-3.1.0.tgz", "integrity": "sha512-+F5+0goFTgZc/k+hqOdsigWO9KifBxCEOkKGsMqPsp8nlOUV2IAueOAUrk9Y3+ThE3Zv2j7dfApYurKHidm2cg=="}}}, "@xhs/i18n-fetch": {"version": "2.0.0-prerelease.12", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/i18n-fetch/-/@xhs/i18n-fetch-2.0.0-prerelease.12.tgz", "integrity": "sha512-qYQ5VLVIeVK4dcK2O0G+lTfWlFLVE5tcGGmBDj4G/bLmY2MaOkDyZ8aHubX92OPyW/ABXg1y2YBezCR14kU6aw==", "requires": {"minimist": "~1.2.8"}}, "@xhs/imagepreviewh5": {"version": "0.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/imagepreviewh5/-/@xhs/imagepreviewh5-0.0.5.tgz", "integrity": "sha512-iGLgF58Z3+spyRJ3FXhLJOwLu2yXvUrCTwXOlZA2EGhCZ2Jt8ubFqEEARfQtzoQ0za1n55MR+Gf9GaXKZneegQ==", "requires": {"@xhs/reds-h5-next": "0.3.1-beta202504211041"}, "dependencies": {"@babel/runtime": {"version": "7.25.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/runtime/download/@babel/runtime-7.25.7.tgz", "integrity": "sha1-f/tTw3qPJHyMTTNeic3xai4ND7Y=", "requires": {"regenerator-runtime": "^0.14.0"}}, "@xhs/reds-h5-next": {"version": "0.3.1-beta202504211041", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-h5-next/-/@xhs/reds-h5-next-0.3.1-beta202504211041.tgz", "integrity": "sha512-rnQTxtdnymvYNwJrSI7wEXO8buGI7vGe9CXz8Mt/AqCJWhmLjKIdN3st75MXiB9pYqYFLJhTT9MSLGB/S+e/3Q==", "requires": {"@babel/runtime": "7.25.7", "@emotion/css": "~11.11.2", "@floating-ui/vue": "~1.1.5", "@types/runes": "~0.4.3", "@xhs/i18n-fetch": "2.0.0-prerelease.12", "@xhs/reds-apm": ">=0.0.1", "async-validator": "~4.2.5", "dayjs": "^1.10.7", "intersection-observer": "~0.12.2", "runes": "~0.4.3"}}, "regenerator-runtime": {"version": "0.14.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz", "integrity": "sha1-NWreECY/aF3aElEAzYYsHbiVMn8="}}}, "@xhs/launcher-plugin-eaglet": {"version": "5.6.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-eaglet/-/@xhs/launcher-plugin-eaglet-5.6.10.tgz", "integrity": "sha512-4OrSMOatGQjP/nBMq8d+kimgY4zZhNjFrBx3Xq3OAz0OBivKIPs9LQfMMyBK+cyGSf7x+p3dRKUepQozjbc7ZQ==", "requires": {"@xhs/abtest": "^0.7.0", "@xhs/eaglet": "1.1.1-1", "@xhs/eaglet-emitter-base": "^0.1.3", "@xhs/ozone-bridge": "^4.0.3", "@xhs/ozone-schema": "^1.141.0", "@xhs/perf-metrics": "1.10.3-23", "google-protobuf": "3.21.2", "lodash.merge": "^4.6.2", "lodash.uniqueid": "^4.0.1", "uuid": "^8.3.2"}}, "@xhs/logger": {"version": "2.6.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logger/-/@xhs/logger-2.6.2.tgz", "integrity": "sha1-jB0jt3d4XxaeHGyTTEJQHPge+1Y="}, "@xhs/onix-css": {"version": "0.0.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/onix-css/-/@xhs/onix-css-0.0.7.tgz", "integrity": "sha512-IgwL3YAifZlg45gNR+05LZYGN6UHyjg8YbGiUqI4oIvEViH1mvwvII9FZ/ZRe6FV1IgzzKrPlVeBNzG5FIERuQ==", "requires": {"normalize.css": "^8.0.1", "sacss": "^1.1.0"}}, "@xhs/onix-icon": {"version": "10.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/onix-icon/-/@xhs/onix-icon-10.0.0.tgz", "integrity": "sha1-p1obhsMM4T7WG5DNWiRSfst4IOY="}, "@xhs/ozone-bridge": {"version": "4.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-4.0.5.tgz", "integrity": "sha512-wQ137rbDz9DEpRd55HWODmH1J7PwQmwhAcIO9uJ6z3LgUbrXJpKJtGUoYZFyXQ9Cmfrkxh6wqOHpFNZyRgHTIQ==", "requires": {"@xhs/bridge-shared": "^1.1.0", "@xhs/data-transform": "^0.3.3", "@xhs/logger": "^2.6.2", "@xhs/ozone-detector": "^3.4.4", "@xhs/ozone-schema": "^1.131.0", "prop-types": "^15.7.2", "uuid": "^8.3.2"}}, "@xhs/ozone-detector": {"version": "3.16.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-detector/-/@xhs/ozone-detector-3.16.1.tgz", "integrity": "sha512-PTPivKA/R4oG8zMPx79K4T/1swulgtXBssSS4lNObTD3xOZcpECTMkNKOEwgxLXsMMmV36JCSrDxPx7gBPlnvw=="}, "@xhs/ozone-schema": {"version": "1.325.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-schema/-/@xhs/ozone-schema-1.325.0.tgz", "integrity": "sha512-g5NMxhM6WAPWAQh9L6/WyyE+Om5A0E7HCdjnSy4MmuIZa0XWPkLC2ueYDUc4LTElWgkmuQdf/ih5Nfot7OsFRA==", "requires": {"@types/uuid": "^8.3.0", "@xhs/data-transform": "^0.3.3", "@xhs/logger": "^2.6.2", "@xhs/ozone-detector": "^3.15.3", "jsonschema": "1.4.1", "prop-types": "^15.8.1", "url": "^0.11.0", "uuid": "^8.1.0"}}, "@xhs/ozone-sdk": {"version": "2.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-sdk/-/@xhs/ozone-sdk-2.1.1.tgz", "integrity": "sha512-z/DxAXxfiIyKySD6MtOovkY9aPh+xHhP7p4QySkoxYe1u9QoBWKJ2kb25RlP77yrX9Y3arBJWKAdAAJq0gJ7LA==", "requires": {"@xhs/http": "^1.2.2", "@xhs/ozone-detector": "^3.2.0"}}, "@xhs/ozone-share": {"version": "1.6.1-0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-share/-/@xhs/ozone-share-1.6.1-0.tgz", "integrity": "sha512-QuCjvxLuG79NfZoITgdZwgDGuysOZSPO227bUOSJIQqlQR+BxDUj3dOJ1byxMQwwzO6XiDLshb0eos4ZJqLWOA==", "requires": {"@types/url-parse": "^1.4.3", "@xhs/ozone-detector": "^3.15.5", "@xhs/ozone-sdk": "^2.1.1", "lodash.throttle": "^4.1.1", "url-parse": "^1.4.7"}}, "@xhs/paint-timing": {"version": "0.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/paint-timing/-/@xhs/paint-timing-0.3.0.tgz", "integrity": "sha1-ZwS3QvMjeUESl0HnOjPmEXheIBM="}, "@xhs/perf-metrics": {"version": "1.10.3-23", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/perf-metrics/-/@xhs/perf-metrics-1.10.3-23.tgz", "integrity": "sha1-WzJ2lFmRRiWZIU9BZ9A2I0lUi8o=", "requires": {"@xhs/paint-timing": "^0.3.0", "uuid": "^8.3.1"}}, "@xhs/protobuf-pages-lifeseller-tracker": {"version": "0.7.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/protobuf-pages-lifeseller-tracker/-/@xhs/protobuf-pages-lifeseller-tracker-0.7.1.tgz", "integrity": "sha512-zg/w7+6dir6iCZocsqV8KaX5GMxWZTKM0wqBPvetI2IP3/ryBheqn0asK8DMyqUc5DcP+PAo5Mmeb8EAYMo5Jg==", "requires": {"@xhs/eaglet": "^1.0.0", "@xhs/formula-babel-register": "^1.0.0", "@xhs/google-protobuf": "^0.1.1", "google-protobuf": "3.21.2", "prettier-standalone": "^1.3.1-0"}, "dependencies": {"@xhs/eaglet": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/eaglet/-/@xhs/eaglet-1.1.0.tgz", "integrity": "sha1-1zXyjJUpnW8WyMzZrQnbhV2aTOc="}}}, "@xhs/reder-icon-svg-ReDs_icon": {"version": "1.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reder-icon-svg-ReDs_icon/-/@xhs/reder-icon-svg-ReDs_icon-1.0.8.tgz", "integrity": "sha512-01HSa47MMVUGQptrlf+zQBVQ+k8bJFQHngQasDhXMgXWEc/jorEWJOBSI4BcdeyPWrbhhE8CgfA7EkBHaUuACw=="}, "@xhs/reds-alert-web": {"version": "2.2.22", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-alert-web/-/@xhs/reds-alert-web-2.2.22.tgz", "integrity": "sha512-O19REuVuIIp6RkRz8SJblA9tJEQfZ9U2iD2OF76+h0je+N+T+5HFNaobAk4GDm1yDy5qZuLQYU2NklID+ZafHw==", "requires": {"@xhs/ozone-bridge": "^3.25.1", "@xhs/ozone-detector": "^3.11.2", "@xhs/ozone-schema": "1.8.6", "@xhs/reds-modal-web": "^2.6.5", "@xhs/reds-utils-web": "^2.0.1"}, "dependencies": {"@xhs/ozone-bridge": {"version": "3.26.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-3.26.3.tgz", "integrity": "sha512-biiAdAx6jkncQipy75mtBnd29j1I2Zo4ibMqsSNse8FP0KoGYgZTRFOfmXbaAmjbZqPEBUcdupCK5WwecumgPw==", "requires": {"@xhs/bridge-shared": "^1.1.0", "@xhs/data-transform": "^0.3.3", "@xhs/logger": "^2.2.2", "@xhs/ozone-detector": "^3.15.3", "@xhs/ozone-schema": "^1.163.0", "prop-types": "^15.7.2", "uuid": "^7.0.2"}, "dependencies": {"@xhs/ozone-schema": {"version": "1.371.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-schema/-/@xhs/ozone-schema-1.371.0.tgz", "integrity": "sha512-VAIelibsMYAgVbcy9/oozWrcYmSgxTzJgalbSF41HupQg4XG1pJb/3CflfWEIg6Ewaol38hGB4ABR54mA5XD9g==", "requires": {"@types/uuid": "^8.3.0", "@xhs/data-transform": "^0.3.3", "@xhs/logger": "^2.6.2", "@xhs/ozone-detector": "^3.15.3", "jsonschema": "1.4.1", "prop-types": "^15.8.1", "url": "^0.11.0", "uuid": "^8.1.0"}, "dependencies": {"uuid": {"version": "8.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="}}}}}, "@xhs/ozone-schema": {"version": "1.8.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-schema/-/@xhs/ozone-schema-1.8.6.tgz", "integrity": "sha1-ys9KKBIKeNT07YNdby7W1XoYbJk=", "requires": {"@types/uuid": "^8.3.0", "@xhs/logger": "^2.5.1", "@xhs/ozone-detector": "^3.4.5", "jsonschema": "^1.2.6", "url": "^0.11.0", "uuid": "^8.1.0"}, "dependencies": {"uuid": {"version": "8.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="}}}, "uuid": {"version": "7.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-7.0.3.tgz", "integrity": "sha1-xcnyyM8l3Ao3LE3xRBxB9b0MaAs="}}}, "@xhs/reds-apm": {"version": "0.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-apm/-/@xhs/reds-apm-0.0.1.tgz", "integrity": "sha512-5jkV7iYFMhwOqemcZ5dkQMmaw38dkR9jkji775A5MRb+0ivBmG8grAB37Xb4Gx+aA9Nc/TRVk0+juICN283wjw=="}, "@xhs/reds-avatar-web": {"version": "2.2.19", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-avatar-web/-/@xhs/reds-avatar-web-2.2.19.tgz", "integrity": "sha512-jSsQ6WDt8hdnDBSkKQT6hGacXCLQ29NAPvOPpIA4SxF+/xnrh7qleZbuMbafYEtaK4Q/fL4//Odad7v875kC8g==", "requires": {"@xhs/reds-image-web": "^2.10.1"}}, "@xhs/reds-composition-web": {"version": "2.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-composition-web/-/@xhs/reds-composition-web-2.2.0.tgz", "integrity": "sha1-PCp+YpVKPb5nxDS9QYBZdq2l1rE=", "requires": {"intersection-observer": "^0.12.0"}}, "@xhs/reds-h5": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-h5/-/@xhs/reds-h5-1.1.0.tgz", "integrity": "sha512-JIW0+XM/PZumfLJ/I5MNQyQio6NzCdfEwaEFtIhFvsOS1+mby4qhV/s6bV3QL1UpCZENNguVdRgTMFHJI8JuIQ==", "requires": {"@babel/types": "^7.16.8", "@icon-park/svg": "^1.4.1", "@types/node": "^14.17.1", "@vueuse/core": "^9.3.1", "@xhs/ozone-detector": "^3.11.4", "@xhs/ozone-schema": "^1.162.0", "@xhs/reds-h5-style": "^2.3.2", "@xhs/reds-theme-web": "^2.1.0", "@xhs/tenet-bridge": "^0.3.7", "async-validator": "^4.2.5", "bezier-easing": "^2.1.0", "dayjs": "^1.10.7", "flv.js": "1.5.0", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.mergewith": "^4.6.2", "lodash.set": "^4.3.2", "lodash.throttle": "^4.1.1", "lodash.uniq": "^4.5.0", "resize-observer-polyfill": "^1.5.1", "runes": "^0.4.3"}, "dependencies": {"@types/node": {"version": "14.18.63", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/download/@types/node-14.18.63.tgz", "integrity": "sha1-F4j6jag427X56plLg0J4IF22yis="}}}, "@xhs/reds-h5-next": {"version": "0.5.1-beta202506261108", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-h5-next/-/@xhs/reds-h5-next-0.5.1-beta202506261108.tgz", "integrity": "sha512-q84QEcj0HtKixmAGjE3HjX7zjqeK21zwdDWZDM/55HxNoN32Fd3fhY+79xv4zAGiJKgFkqN3+kW5LP9GbtU4lA==", "requires": {"@babel/runtime": "7.25.7", "@emotion/css": "~11.11.2", "@floating-ui/vue": "~1.1.5", "@types/runes": "~0.4.3", "@xhs/i18n-fetch": "2.0.0-prerelease.12", "@xhs/reds-apm": "0.0.2", "async-validator": "~4.2.5", "dayjs": "^1.10.7", "intersection-observer": "~0.12.2", "runes": "~0.4.3"}, "dependencies": {"@babel/runtime": {"version": "7.25.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/runtime/download/@babel/runtime-7.25.7.tgz", "integrity": "sha1-f/tTw3qPJHyMTTNeic3xai4ND7Y=", "requires": {"regenerator-runtime": "^0.14.0"}}, "@xhs/reds-apm": {"version": "0.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-apm/-/@xhs/reds-apm-0.0.2.tgz", "integrity": "sha512-35UTE1/QPorXHxHahY+3/rjrbhPynLIg5VdA1gYqi6jSWibMnmynkXH47wOmn2wSF7eckt2dxeUIF1IsJyl2Wg=="}, "regenerator-runtime": {"version": "0.14.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz", "integrity": "sha1-NWreECY/aF3aElEAzYYsHbiVMn8="}}}, "@xhs/reds-h5-style": {"version": "2.3.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-h5-style/-/@xhs/reds-h5-style-2.3.8.tgz", "integrity": "sha512-KHGz2mwfV5eSgrP5cw7yETdsZlzJAxgg3ANKvUBtaHLLk8WNIuV/xuFN7XEwa/vOkFtLotCveXbcU9FA49BcFg==", "requires": {"normalize.css": "^8.0.1", "postcss-pxtorem": "^6.0.0"}, "dependencies": {"postcss-pxtorem": {"version": "6.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss-pxtorem/-/postcss-pxtorem-6.1.0.tgz", "integrity": "sha512-R<PERSON>ODSNci9ADal3zUcPHOF/K83TiCgNSPXQFSbwyPHNV8ioHIE4SaC+FPOufd8jsr5jV2uIz29v1Uqy1c4ov42g=="}}}, "@xhs/reds-image-web": {"version": "2.10.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-image-web/-/@xhs/reds-image-web-2.10.1.tgz", "integrity": "sha512-cTRAtseA8JCZZ+ZogrUnoG6atOjGSCsSwrSzRMCC8bNPUDJZwthWvtaFkQV+wPArb7gZQchn9W58hxOyMAbu4Q==", "requires": {"@xhs/reds-composition-web": "^2.2.0"}}, "@xhs/reds-modal-web": {"version": "2.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-modal-web/-/@xhs/reds-modal-web-2.7.0.tgz", "integrity": "sha512-SwooqtirSedbFr09geiVfgwZpS5m4sKAK0IaILImO/V1VQ6mA6/uFRiuK8kxVxO6D91L2I8ZcWIEDxQbHyKmWg==", "requires": {"@xhs/reds-avatar-web": "^2.2.19", "@xhs/reds-style-web": "^2.4.0", "@xhs/reds-text-web": "^2.4.0"}}, "@xhs/reds-style-web": {"version": "2.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-style-web/-/@xhs/reds-style-web-2.4.0.tgz", "integrity": "sha512-6ZptqZ9xwEi75hKIrcmmtGlV2JTFnRHKEjuw9IpSt5Wa0AXWo+ZP8MKOYnsJrjU2FNG7iwzMtr2iikaCEqQyeQ==", "requires": {"@xhs/reds-token": "^1.2.1", "normalize.css": "^8.0.1", "postcss-pxtorem": "^6.0.0"}, "dependencies": {"postcss-pxtorem": {"version": "6.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss-pxtorem/-/postcss-pxtorem-6.1.0.tgz", "integrity": "sha512-R<PERSON>ODSNci9ADal3zUcPHOF/K83TiCgNSPXQFSbwyPHNV8ioHIE4SaC+FPOufd8jsr5jV2uIz29v1Uqy1c4ov42g=="}}}, "@xhs/reds-text-web": {"version": "2.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-text-web/-/@xhs/reds-text-web-2.4.0.tgz", "integrity": "sha512-62KgrPo5RD+XJdJ6nf09aatLrrPs+5vFJxHvWOvl2dr3orTY0IWB5kLa30FhExkaEahcTgNfmsyHZHIz7wrvhg==", "requires": {"@xhs/onix-css": "^0.0.7", "@xhs/reds-theme-web": "^2.1.0"}}, "@xhs/reds-theme-web": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-theme-web/-/@xhs/reds-theme-web-2.1.0.tgz", "integrity": "sha1-PRcdawa3XmzHF2qSk0FDAUbs3ZE=", "requires": {"@xhs/reds-token": "^1.2.1"}}, "@xhs/reds-token": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-token/-/@xhs/reds-token-1.2.1.tgz", "integrity": "sha1-d1wrZ9zlr0LSbA5d17pDNoDMggg=", "requires": {"npm-run-all": "^4.1.5"}}, "@xhs/reds-token-next": {"version": "0.5.1-beta202506261108", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-token-next/-/@xhs/reds-token-next-0.5.1-beta202506261108.tgz", "integrity": "sha512-KgxXiitC6yDHAFmdnGEWaR+i5Ovcl33W5bfmtywGSdzAdZUH0Hfr5ZbUyp85e7Sl9gYo57EjjBbNceaE5ThNvw=="}, "@xhs/reds-utils-web": {"version": "2.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/reds-utils-web/-/@xhs/reds-utils-web-2.0.1.tgz", "integrity": "sha1-uC0T14Obqky93WMJIA46C2tBOek="}, "@xhs/rex-uploader": {"version": "2.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/rex-uploader/-/@xhs/rex-uploader-2.0.4.tgz", "integrity": "sha1-8rmXomS0Bcs4N1oDrsbSi0XQaP8=", "requires": {"@xhs/ozone-schema": "^1.92.0"}}, "@xhs/riant": {"version": "1.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/riant/-/@xhs/riant-1.1.2.tgz", "integrity": "sha1-x51HMw5KxbF6giER1xxWtqGTdIA=", "requires": {"@riant/icons": "^0.1.2", "@riant/use": "^0.1.2", "@vant/popperjs": "^1.1.0"}}, "@xhs/tenet-bridge": {"version": "0.3.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/tenet-bridge/-/@xhs/tenet-bridge-0.3.7.tgz", "integrity": "sha512-1WpykmcbsmNbEO44tdQPiUKWIv2p1k1vr07EQ+5R2A/32sCPFKTnUQYRHBR5XdT4Mh3nfTVeoXO7KLgx+8IEJQ==", "requires": {"@types/lodash.throttle": "^4.1.7", "@xhs/ozone-bridge": "^4.0.4", "@xhs/ozone-detector": "^3.11.8", "@xhs/ozone-schema": "^1.137.0", "@xhs/reds-alert-web": "2.2.22", "lodash.throttle": "^4.1.1"}}, "@xhs/untrace": {"version": "0.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/untrace/-/@xhs/untrace-0.0.1.tgz", "integrity": "sha1-TaUM+MY+UouU7VL42GZc8Dxv8zk=", "requires": {"long": "^5.2.3"}}, "@xhs/water-kaipingyidongBduan": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/water-kaipingyidongBduan/-/@xhs/water-kaipingyidongBduan-1.0.0.tgz", "integrity": "sha512-AGUMNVf82gQxiSMzdxsWpUEV1feYv3+F1Yu+Rg5Hk+3R3gEQRC26Mqrsqt2uQwOtowo0JUwtvaXBhpYRNdT4wA=="}, "@xhs/water-reds-dark": {"version": "1.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/water-reds-dark/-/@xhs/water-reds-dark-1.4.0.tgz", "integrity": "sha512-E<PERSON>ayuINYSSjNLHZIaiVR13hEfxNEHSub2EPCL55SFKSwNLkYUm0NcatoiDYA9wS+D1iWjLXZCaCmw0p89tNwGg=="}, "@xhs/water-reds-light": {"version": "1.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/water-reds-light/-/@xhs/water-reds-light-1.4.0.tgz", "integrity": "sha512-GBr521JOhYikL4Lc/d2fScIP9G990NS2omT69K4b7//T4tugoDRZeZtto6RqEzFPED3h5+65jwQ/SmEUj4Ikng=="}, "@xhs/water-theme-professional-account-platform": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/water-theme-professional-account-platform/-/@xhs/water-theme-professional-account-platform-1.0.0.tgz", "integrity": "sha512-akPTtPZlNN72TXFkG/7qr0A+8qMVP58xek/2bqpOZcxRVI31c2XgV7/R9LRtOG/OSlGQHwPDjmXYvTZpI0y+ow=="}, "ansi-escapes": {"version": "4.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-escapes/download/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "requires": {"type-fest": "^0.21.3"}}, "ansi-regex": {"version": "2.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "ansi-styles": {"version": "2.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="}, "anymatch": {"version": "3.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/anymatch/download/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "argparse": {"version": "1.0.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "requires": {"sprintf-js": "~1.0.2"}}, "array-buffer-byte-length": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "integrity": "sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==", "requires": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}}, "arraybuffer.prototype.slice": {"version": "1.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz", "integrity": "sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==", "requires": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}}, "async-function": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async-function/-/async-function-1.0.0.tgz", "integrity": "sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA=="}, "async-validator": {"version": "4.2.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async-validator/download/async-validator-4.2.5.tgz", "integrity": "sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk="}, "available-typed-arrays": {"version": "1.0.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==", "requires": {"possible-typed-array-names": "^1.0.0"}}, "axios": {"version": "0.19.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/axios/download/axios-0.19.2.tgz", "integrity": "sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=", "requires": {"follow-redirects": "1.5.10"}}, "babel-code-frame": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-code-frame/download/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "requires": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}, "dependencies": {"js-tokens": {"version": "3.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-tokens/download/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls="}}}, "babel-helper-builder-binary-assignment-operator-visitor": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz", "integrity": "sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=", "requires": {"babel-helper-explode-assignable-expression": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-call-delegate": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz", "integrity": "sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=", "requires": {"babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helper-define-map": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz", "integrity": "sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=", "requires": {"babel-helper-function-name": "^6.24.1", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}}, "babel-helper-explode-assignable-expression": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz", "integrity": "sha1-8luCz33BBDPFX3BZLVdGQArCLKo=", "requires": {"babel-runtime": "^6.22.0", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helper-function-name": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz", "integrity": "sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=", "requires": {"babel-helper-get-function-arity": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helper-get-function-arity": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz", "integrity": "sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-hoist-variables": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz", "integrity": "sha1-HssnaJydJVE+rbyZFKc/VAi+enY=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-optimise-call-expression": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz", "integrity": "sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-helper-regex": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz", "integrity": "sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=", "requires": {"babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}}, "babel-helper-remap-async-to-generator": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz", "integrity": "sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=", "requires": {"babel-helper-function-name": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-helper-replace-supers": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz", "integrity": "sha1-v22/5Dk40XNpohPKiov3S2qQqxo=", "requires": {"babel-helper-optimise-call-expression": "^6.24.1", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-jest": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-jest/-/babel-jest-29.7.0.tgz", "integrity": "sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==", "requires": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "babel-messages": {"version": "6.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-messages/download/babel-messages-6.23.0.tgz", "integrity": "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-check-es2015-constants": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz", "integrity": "sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-istanbul": {"version": "6.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz", "integrity": "sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=", "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "dependencies": {"istanbul-lib-instrument": {"version": "5.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz", "integrity": "sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=", "requires": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}}}}, "babel-plugin-jest-hoist": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "integrity": "sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==", "requires": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}}, "babel-plugin-macros": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-macros/download/babel-plugin-macros-3.1.0.tgz", "integrity": "sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=", "requires": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}}, "babel-plugin-polyfill-corejs2": {"version": "0.4.13", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz", "integrity": "sha512-3sX/eOms8kd3q2KZ6DAhKPc0dgm525Gqq5NtWKZ7QYYZEv57OQ54KtblzJzH1lQF/eQxO8KjWGIK9IPUJNus5g==", "requires": {"@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.4", "semver": "^6.3.1"}}, "babel-plugin-polyfill-corejs3": {"version": "0.11.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz", "integrity": "sha512-yGCqvBT4rwMczo28xkH/noxJ6MZ4nJfkVYdoDaC/utLtWrXxv27HVrzAeSbqR8SxDsp46n0YF47EbHoixy6rXQ==", "requires": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}}, "babel-plugin-polyfill-regenerator": {"version": "0.6.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz", "integrity": "sha512-7gD3pRadPrbjhjLyxebmx/WrFYcuSjZ0XbdUujQMZ/fcE9oeewk2U/7PCvez84UeuK3oSjmPZ0Ch0dlupQvGzw==", "requires": {"@babel/helper-define-polyfill-provider": "^0.6.4"}}, "babel-plugin-syntax-async-functions": {"version": "6.13.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz", "integrity": "sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU="}, "babel-plugin-syntax-exponentiation-operator": {"version": "6.13.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz", "integrity": "sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4="}, "babel-plugin-syntax-trailing-function-commas": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz", "integrity": "sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM="}, "babel-plugin-transform-async-to-generator": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz", "integrity": "sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=", "requires": {"babel-helper-remap-async-to-generator": "^6.24.1", "babel-plugin-syntax-async-functions": "^6.8.0", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-arrow-functions": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz", "integrity": "sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-block-scoped-functions": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz", "integrity": "sha1-u8UbSflk1wy42OC5ToICRs46YUE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-block-scoping": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz", "integrity": "sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=", "requires": {"babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "lodash": "^4.17.4"}}, "babel-plugin-transform-es2015-classes": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz", "integrity": "sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=", "requires": {"babel-helper-define-map": "^6.24.1", "babel-helper-function-name": "^6.24.1", "babel-helper-optimise-call-expression": "^6.24.1", "babel-helper-replace-supers": "^6.24.1", "babel-messages": "^6.23.0", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-computed-properties": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz", "integrity": "sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=", "requires": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-destructuring": {"version": "6.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz", "integrity": "sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-duplicate-keys": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz", "integrity": "sha1-c+s9MQypaePvnskcU3QabxV2Qj4=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-for-of": {"version": "6.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz", "integrity": "sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-function-name": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz", "integrity": "sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=", "requires": {"babel-helper-function-name": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-literals": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz", "integrity": "sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-modules-amd": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz", "integrity": "sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=", "requires": {"babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-modules-commonjs": {"version": "6.26.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz", "integrity": "sha1-WKeThjqefKhwvcWogRF/+sJ9tvM=", "requires": {"babel-plugin-transform-strict-mode": "^6.24.1", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-types": "^6.26.0"}}, "babel-plugin-transform-es2015-modules-systemjs": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz", "integrity": "sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=", "requires": {"babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-modules-umd": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz", "integrity": "sha1-rJl+YoXNGO1hdq22B9YCNErThGg=", "requires": {"babel-plugin-transform-es2015-modules-amd": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-plugin-transform-es2015-object-super": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz", "integrity": "sha1-JM72muIcuDp/hgPa0CH1cusnj40=", "requires": {"babel-helper-replace-supers": "^6.24.1", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-parameters": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz", "integrity": "sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=", "requires": {"babel-helper-call-delegate": "^6.24.1", "babel-helper-get-function-arity": "^6.24.1", "babel-runtime": "^6.22.0", "babel-template": "^6.24.1", "babel-traverse": "^6.24.1", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-shorthand-properties": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz", "integrity": "sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-spread": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz", "integrity": "sha1-1taKmfia7cRTbIGlQujdnxdG+NE=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-sticky-regex": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz", "integrity": "sha1-AMHNsaynERLN8M9hJsLta0V8zbw=", "requires": {"babel-helper-regex": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-plugin-transform-es2015-template-literals": {"version": "6.22.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz", "integrity": "sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-typeof-symbol": {"version": "6.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz", "integrity": "sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=", "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-transform-es2015-unicode-regex": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz", "integrity": "sha1-04sS9C6nMj9yk4fxinxa4frrNek=", "requires": {"babel-helper-regex": "^6.24.1", "babel-runtime": "^6.22.0", "regexpu-core": "^2.0.0"}}, "babel-plugin-transform-exponentiation-operator": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz", "integrity": "sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=", "requires": {"babel-helper-builder-binary-assignment-operator-visitor": "^6.24.1", "babel-plugin-syntax-exponentiation-operator": "^6.8.0", "babel-runtime": "^6.22.0"}}, "babel-plugin-transform-regenerator": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz", "integrity": "sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=", "requires": {"regenerator-transform": "^0.10.0"}}, "babel-plugin-transform-strict-mode": {"version": "6.24.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz", "integrity": "sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=", "requires": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}, "babel-preset-current-node-syntax": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "integrity": "sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==", "requires": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}}, "babel-preset-env": {"version": "1.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-preset-env/download/babel-preset-env-1.7.0.tgz", "integrity": "sha1-3qefpOvriDzTXasH4mDBycBN93o=", "requires": {"babel-plugin-check-es2015-constants": "^6.22.0", "babel-plugin-syntax-trailing-function-commas": "^6.22.0", "babel-plugin-transform-async-to-generator": "^6.22.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.23.0", "babel-plugin-transform-es2015-classes": "^6.23.0", "babel-plugin-transform-es2015-computed-properties": "^6.22.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.22.0", "babel-plugin-transform-es2015-for-of": "^6.23.0", "babel-plugin-transform-es2015-function-name": "^6.22.0", "babel-plugin-transform-es2015-literals": "^6.22.0", "babel-plugin-transform-es2015-modules-amd": "^6.22.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.23.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.23.0", "babel-plugin-transform-es2015-modules-umd": "^6.23.0", "babel-plugin-transform-es2015-object-super": "^6.22.0", "babel-plugin-transform-es2015-parameters": "^6.23.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.22.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-sticky-regex": "^6.22.0", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.23.0", "babel-plugin-transform-es2015-unicode-regex": "^6.22.0", "babel-plugin-transform-exponentiation-operator": "^6.22.0", "babel-plugin-transform-regenerator": "^6.22.0", "browserslist": "^3.2.6", "invariant": "^2.2.2", "semver": "^5.3.0"}, "dependencies": {"semver": {"version": "5.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg="}}}, "babel-preset-jest": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "integrity": "sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==", "requires": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}}, "babel-runtime": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-runtime/download/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "requires": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "babel-template": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-template/download/babel-template-6.26.0.tgz", "integrity": "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=", "requires": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}}, "babel-traverse": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-traverse/download/babel-traverse-6.26.0.tgz", "integrity": "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=", "requires": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "globals": {"version": "9.18.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/globals/download/globals-9.18.0.tgz", "integrity": "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo="}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "babel-types": {"version": "6.26.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-types/download/babel-types-6.26.0.tgz", "integrity": "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=", "requires": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}}, "babylon": {"version": "6.18.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babylon/download/babylon-6.18.0.tgz", "integrity": "sha1-ry87iPpvXB5MY00aD46sT1WzleM="}, "balanced-match": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="}, "bezier-easing": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bezier-easing/download/bezier-easing-2.1.0.tgz", "integrity": "sha1-wE3+i5JtbsrKGBPWn/F5t8ICXYY="}, "brace-expansion": {"version": "1.1.11", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "requires": {"fill-range": "^7.1.1"}}, "browserslist": {"version": "3.2.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/download/browserslist-3.2.8.tgz", "integrity": "sha1-sABTYdZHHw9ZUnl6dvyYXx+Xj8Y=", "requires": {"caniuse-lite": "^1.0.30000844", "electron-to-chromium": "^1.3.47"}}, "bser": {"version": "2.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bser/download/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "requires": {"node-int64": "^0.4.0"}}, "buffer-from": {"version": "1.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/buffer-from/download/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="}, "call-bind": {"version": "1.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/call-bind/download/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "requires": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}}, "call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "requires": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}}, "call-bound": {"version": "1.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/call-bound/download/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "requires": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}}, "callsites": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="}, "camelcase": {"version": "5.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="}, "caniuse-lite": {"version": "1.0.30001702", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001702.tgz", "integrity": "sha512-LoPe/D7zioC0REI5W73PeR1e1MLCipRGq/VkovJnd6Df+QVqT+vT33OXCp8QUd7kA7RZrHWxb1B36OQKI/0gOA=="}, "chalk": {"version": "1.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "char-regex": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/char-regex/download/char-regex-1.0.2.tgz", "integrity": "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8="}, "ci-info": {"version": "3.9.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ci-info/download/ci-info-3.9.0.tgz", "integrity": "sha1-QnmmICinsfJi80c/yWBfXiGMWbQ="}, "cjs-module-lexer": {"version": "1.4.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "integrity": "sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q=="}, "cliui": {"version": "8.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cliui/download/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}}}, "clone-deep": {"version": "4.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/clone-deep/download/clone-deep-4.0.1.tgz", "integrity": "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=", "requires": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}}, "co": {"version": "4.6.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/co/download/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "collect-v8-coverage": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q=="}, "color-convert": {"version": "2.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="}, "commondir": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/commondir/download/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="}, "concat-map": {"version": "0.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "convert-source-map": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/convert-source-map/download/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co="}, "core-js": {"version": "2.6.12", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/core-js/download/core-js-2.6.12.tgz", "integrity": "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="}, "core-js-compat": {"version": "3.43.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/core-js-compat/-/core-js-compat-3.43.0.tgz", "integrity": "sha512-2GML2ZsCc5LR7hZYz4AXmjQw8zuy2T//2QntwdnpuYI7jteT6GVYJL7F6C2C57R7gSYrcqVW3lAALefdbhBLDA==", "requires": {"browserslist": "^4.25.0"}, "dependencies": {"browserslist": {"version": "4.25.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "requires": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "caniuse-lite": {"version": "1.0.30001724", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001724.tgz", "integrity": "sha512-WqJo7p0TbHDOythNTqYujmaJTvtYRZrjpP8TCvH6Vb9CYJerJNKamKzIWOM4BkQatWj9H2lYulpdAQNBe7QhNA=="}, "electron-to-chromium": {"version": "1.5.171", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.171.tgz", "integrity": "sha512-scWpzXEJEMrGJa4Y6m/tVotb0WuvNmasv3wWVzUAeCgKU0ToFOhUW6Z+xWnRQANMYGxN4ngJXIThgBJOqzVPCQ=="}}}, "cosmiconfig": {"version": "7.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cosmiconfig/download/cosmiconfig-7.1.0.tgz", "integrity": "sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=", "requires": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}}, "create-jest": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/create-jest/-/create-jest-29.7.0.tgz", "integrity": "sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==", "requires": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "cross-spawn": {"version": "7.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cross-spawn/download/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "csstype": {"version": "3.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "d3-cloud": {"version": "1.2.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-cloud/-/d3-cloud-1.2.7.tgz", "integrity": "sha512-8TrgcgwRIpoZYQp7s3fGB7tATWfhckRb8KcVd1bOgqkNdkJRDGWfdSf4HkHHzZxSczwQJdSxvfPudwir5IAJ3w==", "requires": {"d3-dispatch": "^1.0.3"}}, "d3-dispatch": {"version": "1.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d3-dispatch/download/d3-dispatch-1.0.6.tgz", "integrity": "sha1-ANN7zuTdjNl3Kd2JOgrCnKq6XVg="}, "data-view-buffer": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/data-view-buffer/-/data-view-buffer-1.0.2.tgz", "integrity": "sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==", "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}}, "data-view-byte-length": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz", "integrity": "sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==", "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}}, "data-view-byte-offset": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz", "integrity": "sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==", "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}}, "dayjs": {"version": "1.11.13", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="}, "debug": {"version": "4.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/download/debug-4.4.0.tgz", "integrity": "sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=", "requires": {"ms": "^2.1.3"}}, "dedent": {"version": "1.6.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dedent/-/dedent-1.6.0.tgz", "integrity": "sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA=="}, "deepmerge": {"version": "4.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/deepmerge/download/deepmerge-4.3.1.tgz", "integrity": "sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo="}, "define-data-property": {"version": "1.1.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/define-data-property/download/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "requires": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}}, "define-properties": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/define-properties/download/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "requires": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "detect-newline": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/detect-newline/download/detect-newline-3.1.0.tgz", "integrity": "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE="}, "diff-sequences": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/diff-sequences/-/diff-sequences-29.6.3.tgz", "integrity": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q=="}, "dunder-proto": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "requires": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}}, "electron-to-chromium": {"version": "1.5.112", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.112.tgz", "integrity": "sha512-oen93kVyqSb3l+ziUgzIOlWt/oOuy4zRmpwestMn4rhFWAoFJeFuCVte9F2fASjeZZo7l/Cif9TiyrdW4CwEMA=="}, "emittery": {"version": "0.13.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/emittery/download/emittery-0.13.1.tgz", "integrity": "sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0="}, "emoji-regex": {"version": "8.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "error-ex": {"version": "1.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "requires": {"is-arrayish": "^0.2.1"}}, "eruda": {"version": "3.4.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/eruda/-/eruda-3.4.1.tgz", "integrity": "sha512-RmaO5yD97URY/9Q0lye3cmmNPoXNKreeePIw7c/zllbscR92CjGFZFuQ70+0fLIvLcKW3Xha8DS8NFhmeNbEBQ==", "dev": true}, "es-abstract": {"version": "1.24.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-abstract/-/es-abstract-1.24.0.tgz", "integrity": "sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==", "requires": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}}, "es-define-property": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo="}, "es-errors": {"version": "1.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8="}, "es-object-atoms": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "requires": {"es-errors": "^1.3.0"}}, "es-set-tostringtag": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "requires": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}}, "es-to-primitive": {"version": "1.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-to-primitive/-/es-to-primitive-1.3.0.tgz", "integrity": "sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==", "requires": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}}, "es-toolkit": {"version": "1.29.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-toolkit/-/es-toolkit-1.29.0.tgz", "integrity": "sha512-GjTll+E6APcfAQA09D89HdT8Qn2Yb+TeDSDBTMcxAo+V+w1amAtCI15LJu4YPH/UCPoSo/F47Gr1LIM0TE0lZA==", "dev": true}, "es6-promise": {"version": "4.2.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es6-promise/download/es6-promise-4.2.8.tgz", "integrity": "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo="}, "esbuild": {"version": "0.23.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esbuild/-/esbuild-0.23.0.tgz", "integrity": "sha512-1lvV17H2bMYda/WaFb2jLPeHU3zml2k4/yagNMG8Q/YtfMjCwEUZa2eXXMgZTVSL5q1n4H7sQ0X6CdJDqqeCFA==", "dev": true, "requires": {"@esbuild/aix-ppc64": "0.23.0", "@esbuild/android-arm": "0.23.0", "@esbuild/android-arm64": "0.23.0", "@esbuild/android-x64": "0.23.0", "@esbuild/darwin-arm64": "0.23.0", "@esbuild/darwin-x64": "0.23.0", "@esbuild/freebsd-arm64": "0.23.0", "@esbuild/freebsd-x64": "0.23.0", "@esbuild/linux-arm": "0.23.0", "@esbuild/linux-arm64": "0.23.0", "@esbuild/linux-ia32": "0.23.0", "@esbuild/linux-loong64": "0.23.0", "@esbuild/linux-mips64el": "0.23.0", "@esbuild/linux-ppc64": "0.23.0", "@esbuild/linux-riscv64": "0.23.0", "@esbuild/linux-s390x": "0.23.0", "@esbuild/linux-x64": "0.23.0", "@esbuild/netbsd-x64": "0.23.0", "@esbuild/openbsd-arm64": "0.23.0", "@esbuild/openbsd-x64": "0.23.0", "@esbuild/sunos-x64": "0.23.0", "@esbuild/win32-arm64": "0.23.0", "@esbuild/win32-ia32": "0.23.0", "@esbuild/win32-x64": "0.23.0"}}, "escalade": {"version": "3.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "esprima": {"version": "4.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="}, "esutils": {"version": "2.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="}, "eventemitter3": {"version": "4.0.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/eventemitter3/download/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=", "dev": true}, "execa": {"version": "5.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/execa/download/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "exit": {"version": "0.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/exit/download/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="}, "expect": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/expect/-/expect-29.7.0.tgz", "integrity": "sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==", "requires": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="}, "fb-watchman": {"version": "2.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fb-watchman/download/fb-watchman-2.0.2.tgz", "integrity": "sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=", "requires": {"bser": "2.1.1"}}, "fecha": {"version": "4.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fecha/download/fecha-4.2.3.tgz", "integrity": "sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0="}, "fill-range": {"version": "7.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "requires": {"to-regex-range": "^5.0.1"}}, "find-cache-dir": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-cache-dir/download/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "requires": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "make-dir": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/make-dir/download/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "requires": {"pify": "^4.0.1", "semver": "^5.6.0"}}, "p-locate": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="}, "pkg-dir": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pkg-dir/download/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "requires": {"find-up": "^3.0.0"}}, "semver": {"version": "5.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg="}}}, "find-root": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-root/download/find-root-1.1.0.tgz", "integrity": "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ="}, "find-up": {"version": "4.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "flat": {"version": "5.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/flat/download/flat-5.0.2.tgz", "integrity": "sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=", "dev": true}, "flv.js": {"version": "1.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/flv.js/download/flv.js-1.5.0.tgz", "integrity": "sha1-+lm+1DkdcENc+odArEDsAHDvmK4=", "requires": {"es6-promise": "^4.2.5", "webworkify": "^1.5.0"}}, "follow-redirects": {"version": "1.5.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/follow-redirects/download/follow-redirects-1.5.10.tgz", "integrity": "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=", "requires": {"debug": "=3.1.0"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/download/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "for-each": {"version": "0.3.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/for-each/-/for-each-0.3.5.tgz", "integrity": "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==", "requires": {"is-callable": "^1.2.7"}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "2.3.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "optional": true}, "function-bind": {"version": "1.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw="}, "function.prototype.name": {"version": "1.1.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/function.prototype.name/-/function.prototype.name-1.1.8.tgz", "integrity": "sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==", "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}}, "functions-have-names": {"version": "1.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/functions-have-names/download/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ="}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/gensync/download/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="}, "get-caller-file": {"version": "2.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="}, "get-intrinsic": {"version": "1.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "requires": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}}, "get-package-type": {"version": "0.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-package-type/download/get-package-type-0.1.0.tgz", "integrity": "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro="}, "get-proto": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "requires": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}}, "get-stream": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-stream/download/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c="}, "get-symbol-description": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-symbol-description/-/get-symbol-description-1.1.0.tgz", "integrity": "sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==", "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}}, "glob": {"version": "7.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "globals": {"version": "11.12.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="}, "globalthis": {"version": "1.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/globalthis/-/globalthis-1.0.4.tgz", "integrity": "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==", "requires": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}}, "google-protobuf": {"version": "3.21.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/google-protobuf/download/google-protobuf-3.21.2.tgz", "integrity": "sha1-RYCivqi7spHuV50f77FNb6MHDqQ="}, "gopd": {"version": "1.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE="}, "graceful-fs": {"version": "4.2.11", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM="}, "has-ansi": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-ansi/download/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "requires": {"ansi-regex": "^2.0.0"}}, "has-bigints": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-bigints/-/has-bigints-1.1.0.tgz", "integrity": "sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg=="}, "has-flag": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="}, "has-property-descriptors": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "requires": {"es-define-property": "^1.0.0"}}, "has-proto": {"version": "1.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-proto/download/has-proto-1.2.0.tgz", "integrity": "sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=", "requires": {"dunder-proto": "^1.0.0"}}, "has-symbols": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg="}, "has-tostringtag": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "requires": {"has-symbols": "^1.0.3"}}, "hasown": {"version": "2.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "requires": {"function-bind": "^1.1.2"}}, "hosted-git-info": {"version": "2.8.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hosted-git-info/download/hosted-git-info-2.8.9.tgz", "integrity": "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k="}, "html-escaper": {"version": "2.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/html-escaper/download/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM="}, "html-rspack-plugin": {"version": "6.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/html-rspack-plugin/-/html-rspack-plugin-6.0.2.tgz", "integrity": "sha512-34ICP1ULwi9uFq0QJzT3jdnIp6RmZozp9PUPaH6YLaM69cy1r7WvSh1Mjqk5XEG2XQoktjbGz2WQPdW5O6jELw==", "dev": true, "requires": {"@rspack/lite-tapable": "^1.0.0"}}, "http-proxy": {"version": "1.18.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-proxy/download/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "dev": true, "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-proxy-middleware/-/http-proxy-middleware-3.0.0.tgz", "integrity": "sha512-36AV1fIaI2cWRzHo+rbcxhe3M3jUDCNzc4D5zRl57sEWRAxdXYtw7FSQKYY6PDKssiAKjLYypbssHk+xs/kMXw==", "dev": true, "requires": {"@types/http-proxy": "^1.17.10", "debug": "^4.3.4", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.5"}}, "human-signals": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/human-signals/download/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA="}, "import-fresh": {"version": "3.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==", "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "dependencies": {"resolve-from": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="}}}, "import-local": {"version": "3.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/import-local/-/import-local-3.2.0.tgz", "integrity": "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==", "requires": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o="}, "inflight": {"version": "1.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "internal-slot": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/internal-slot/-/internal-slot-1.1.0.tgz", "integrity": "sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==", "requires": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}}, "intersection-observer": {"version": "0.12.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/intersection-observer/download/intersection-observer-0.12.2.tgz", "integrity": "sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U="}, "invariant": {"version": "2.2.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/invariant/download/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "requires": {"loose-envify": "^1.0.0"}}, "is-array-buffer": {"version": "3.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "integrity": "sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==", "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}}, "is-arrayish": {"version": "0.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="}, "is-async-function": {"version": "2.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-async-function/-/is-async-function-2.1.1.tgz", "integrity": "sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==", "requires": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}}, "is-bigint": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-bigint/-/is-bigint-1.1.0.tgz", "integrity": "sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==", "requires": {"has-bigints": "^1.0.2"}}, "is-boolean-object": {"version": "1.2.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-boolean-object/-/is-boolean-object-1.2.2.tgz", "integrity": "sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==", "requires": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}}, "is-callable": {"version": "1.2.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-callable/download/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU="}, "is-core-module": {"version": "2.16.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "requires": {"hasown": "^2.0.2"}}, "is-data-view": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-data-view/-/is-data-view-1.0.2.tgz", "integrity": "sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==", "requires": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}}, "is-date-object": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-date-object/-/is-date-object-1.1.0.tgz", "integrity": "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==", "requires": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}}, "is-extglob": {"version": "2.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-finalizationregistry": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz", "integrity": "sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==", "requires": {"call-bound": "^1.0.3"}}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="}, "is-generator-fn": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-generator-fn/download/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg="}, "is-generator-function": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==", "requires": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}}, "is-glob": {"version": "4.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-map": {"version": "2.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-map/-/is-map-2.0.3.tgz", "integrity": "sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw=="}, "is-negative-zero": {"version": "2.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-negative-zero/-/is-negative-zero-2.0.3.tgz", "integrity": "sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw=="}, "is-number": {"version": "7.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="}, "is-number-object": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-number-object/-/is-number-object-1.1.1.tgz", "integrity": "sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==", "requires": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}}, "is-plain-obj": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-plain-obj/download/is-plain-obj-3.0.0.tgz", "integrity": "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=", "dev": true}, "is-plain-object": {"version": "2.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "requires": {"isobject": "^3.0.1"}}, "is-regex": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==", "requires": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}}, "is-set": {"version": "2.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-set/-/is-set-2.0.3.tgz", "integrity": "sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg=="}, "is-shared-array-buffer": {"version": "1.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz", "integrity": "sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==", "requires": {"call-bound": "^1.0.3"}}, "is-stream": {"version": "2.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-stream/download/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="}, "is-string": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-string/-/is-string-1.1.1.tgz", "integrity": "sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==", "requires": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}}, "is-symbol": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-symbol/-/is-symbol-1.1.1.tgz", "integrity": "sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==", "requires": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}}, "is-typed-array": {"version": "1.1.15", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==", "requires": {"which-typed-array": "^1.1.16"}}, "is-weakmap": {"version": "2.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-weakmap/-/is-weakmap-2.0.2.tgz", "integrity": "sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w=="}, "is-weakref": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-weakref/-/is-weakref-1.1.1.tgz", "integrity": "sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==", "requires": {"call-bound": "^1.0.3"}}, "is-weakset": {"version": "2.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-weakset/-/is-weakset-2.0.4.tgz", "integrity": "sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==", "requires": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}}, "isarray": {"version": "2.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isarray/download/isarray-2.0.5.tgz", "integrity": "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM="}, "isexe": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isobject": {"version": "3.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "istanbul-lib-coverage": {"version": "3.2.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "integrity": "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg=="}, "istanbul-lib-instrument": {"version": "6.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "integrity": "sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==", "requires": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "dependencies": {"semver": {"version": "7.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="}}}, "istanbul-lib-report": {"version": "3.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==", "requires": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "dependencies": {"supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "istanbul-lib-source-maps": {"version": "4.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz", "integrity": "sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=", "requires": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}}, "istanbul-reports": {"version": "3.1.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==", "requires": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}}, "javascript-stringify": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/javascript-stringify/download/javascript-stringify-2.1.0.tgz", "integrity": "sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk=", "dev": true}, "jest": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest/-/jest-29.7.0.tgz", "integrity": "sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==", "requires": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "jest-cli": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-cli/-/jest-cli-29.7.0.tgz", "integrity": "sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==", "requires": {"@jest/core": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "chalk": "^4.0.0", "create-jest": "^29.7.0", "exit": "^0.1.2", "import-local": "^3.0.2", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "yargs": "^17.3.1"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-changed-files": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "integrity": "sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==", "requires": {"execa": "^5.0.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0"}, "dependencies": {"p-limit": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-limit/download/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "requires": {"yocto-queue": "^0.1.0"}}}}, "jest-circus": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-circus/-/jest-circus-29.7.0.tgz", "integrity": "sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==", "requires": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "p-limit": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-limit/download/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "requires": {"yocto-queue": "^0.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-config": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-config/-/jest-config-29.7.0.tgz", "integrity": "sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==", "requires": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.7.0", "@jest/types": "^29.6.3", "babel-jest": "^29.7.0", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-runner": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-diff": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-diff/-/jest-diff-29.7.0.tgz", "integrity": "sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==", "requires": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-docblock": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-docblock/-/jest-docblock-29.7.0.tgz", "integrity": "sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==", "requires": {"detect-newline": "^3.0.0"}}, "jest-each": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-each/-/jest-each-29.7.0.tgz", "integrity": "sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==", "requires": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-environment-node": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "integrity": "sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==", "requires": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}}, "jest-get-type": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-get-type/-/jest-get-type-29.6.3.tgz", "integrity": "sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw=="}, "jest-haste-map": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "integrity": "sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==", "requires": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "fsevents": "^2.3.2", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}}, "jest-leak-detector": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "integrity": "sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==", "requires": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}}, "jest-matcher-utils": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "integrity": "sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==", "requires": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-message-util": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-message-util/-/jest-message-util-29.7.0.tgz", "integrity": "sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==", "requires": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-mock": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-mock/-/jest-mock-29.7.0.tgz", "integrity": "sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==", "requires": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}}, "jest-pnp-resolver": {"version": "1.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-pnp-resolver/download/jest-pnp-resolver-1.2.3.tgz", "integrity": "sha1-kwsVRhZNStWTfVVA5xHU041MrS4="}, "jest-regex-util": {"version": "29.6.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "integrity": "sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg=="}, "jest-resolve": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-resolve/-/jest-resolve-29.7.0.tgz", "integrity": "sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==", "requires": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-resolve-dependencies": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "integrity": "sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==", "requires": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}}, "jest-runner": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-runner/-/jest-runner-29.7.0.tgz", "integrity": "sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==", "requires": {"@jest/console": "^29.7.0", "@jest/environment": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-message-util": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0", "jest-util": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "p-limit": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-limit/download/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "requires": {"yocto-queue": "^0.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-runtime": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-runtime/-/jest-runtime-29.7.0.tgz", "integrity": "sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==", "requires": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/source-map": "^29.6.3", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-snapshot": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "integrity": "sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==", "requires": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.7.0", "graceful-fs": "^4.2.9", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "natural-compare": "^1.4.0", "pretty-format": "^29.7.0", "semver": "^7.5.3"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "semver": {"version": "7.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-util": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-util/-/jest-util-29.7.0.tgz", "integrity": "sha512-z6<PERSON>bKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==", "requires": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-validate": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-validate/-/jest-validate-29.7.0.tgz", "integrity": "sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==", "requires": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "camelcase": {"version": "6.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/camelcase/download/camelcase-6.3.0.tgz", "integrity": "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo="}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-watcher": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-watcher/-/jest-watcher-29.7.0.tgz", "integrity": "sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==", "requires": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}}}, "jest-worker": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-worker/-/jest-worker-29.7.0.tgz", "integrity": "sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==", "requires": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "dependencies": {"supports-color": {"version": "8.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "requires": {"has-flag": "^4.0.0"}}}}, "jiti": {"version": "2.3.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jiti/-/jiti-2.3.3.tgz", "integrity": "sha512-EX4oNDwcXSivPrw2qKH2LB5PoFxEvgtv2JgwW0bU858HoLQ+kutSvjLMUqBd0PeJYEinLWhoI9Ol0eYMqj/wNQ==", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "js-yaml": {"version": "3.14.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsesc": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA=="}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="}, "json-stable-stringify": {"version": "1.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-stable-stringify/-/json-stable-stringify-1.3.0.tgz", "integrity": "sha512-qtYiSSFlwot9XHtF9bD9c7rwKjr+RecWT//ZnPvSmEjpV5mmPOCN4j8UjY5hbjNkOwZ/jQv3J6R1/pL7RwgMsg==", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "isarray": "^2.0.5", "jsonify": "^0.0.1", "object-keys": "^1.1.1"}}, "json5": {"version": "2.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json5/download/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM="}, "jsonify": {"version": "0.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsonify/download/jsonify-0.0.1.tgz", "integrity": "sha1-KqMRHa49NKDxUcY/OkXZldlCCXg=", "dev": true}, "jsonschema": {"version": "1.4.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsonschema/download/jsonschema-1.4.1.tgz", "integrity": "sha1-zEw/AHf7RUKYKXPYoIO2s09ILas="}, "kind-of": {"version": "6.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/kind-of/download/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="}, "kleur": {"version": "3.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/kleur/download/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4="}, "leven": {"version": "3.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/leven/download/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I="}, "lines-and-columns": {"version": "1.2.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lines-and-columns/download/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI="}, "load-json-file": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/load-json-file/download/load-json-file-4.0.0.tgz", "integrity": "sha1-L19Fq5HjMhYjT9U62rZo607AmTs=", "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}, "dependencies": {"parse-json": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "pify": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}, "strip-bom": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-bom/download/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="}}}, "locate-path": {"version": "5.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.21", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "lodash.clonedeep": {"version": "4.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz", "integrity": "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8="}, "lodash.debounce": {"version": "4.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.debounce/download/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168="}, "lodash.get": {"version": "4.4.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.get/download/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk="}, "lodash.merge": {"version": "4.6.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.merge/download/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="}, "lodash.mergewith": {"version": "4.6.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.mergewith/download/lodash.mergewith-4.6.2.tgz", "integrity": "sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U="}, "lodash.set": {"version": "4.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.set/download/lodash.set-4.3.2.tgz", "integrity": "sha1-2HV7HagH3eJIFrDWqEvqGnYjCyM="}, "lodash.throttle": {"version": "4.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.throttle/download/lodash.throttle-4.1.1.tgz", "integrity": "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ="}, "lodash.uniq": {"version": "4.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.uniq/download/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="}, "lodash.uniqueid": {"version": "4.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.uniqueid/download/lodash.uniqueid-4.0.1.tgz", "integrity": "sha1-MmjyanyI5PSxdY1nknGBTjH6WyY="}, "long": {"version": "5.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/long/-/long-5.3.2.tgz", "integrity": "sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA=="}, "loose-envify": {"version": "1.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/loose-envify/download/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lru-cache": {"version": "5.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/make-dir/download/make-dir-4.0.0.tgz", "integrity": "sha1-w8IwencSd82WODBfkVwprnQbYU4=", "requires": {"semver": "^7.5.3"}, "dependencies": {"semver": {"version": "7.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="}}}, "makeerror": {"version": "1.0.12", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/makeerror/download/makeerror-1.0.12.tgz", "integrity": "sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=", "requires": {"tmpl": "1.0.5"}}, "math-intrinsics": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k="}, "memorystream": {"version": "0.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/memorystream/download/memorystream-0.3.1.tgz", "integrity": "sha1-htcJCzDORV1j+64S3aUaR93K+bI="}, "merge-stream": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/merge-stream/download/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="}, "micromatch": {"version": "4.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromatch/download/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "requires": {"braces": "^3.0.3", "picomatch": "^2.3.1"}}, "mimic-fn": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimist/download/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw="}, "module-alias": {"version": "2.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/module-alias/download/module-alias-2.2.3.tgz", "integrity": "sha1-7C6Fxolzvaarcc58k7dj7JYFMiE=", "dev": true}, "ms": {"version": "2.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}, "nanoid": {"version": "3.3.11", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/nanoid/download/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "dev": true}, "natural-compare": {"version": "1.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="}, "nice-try": {"version": "1.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="}, "node-int64": {"version": "0.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-int64/download/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="}, "node-releases": {"version": "2.0.19", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="}, "normalize-package-data": {"version": "2.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"semver": {"version": "5.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg="}}}, "normalize-path": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="}, "normalize.css": {"version": "8.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/normalize.css/download/normalize.css-8.0.1.tgz", "integrity": "sha1-m5iiCHOLnMJjTKrLxC0THJdIe/M="}, "npm-run-all": {"version": "4.1.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/npm-run-all/download/npm-run-all-4.1.5.tgz", "integrity": "sha1-BEdiAqFe4OLiFAgIYb/xKlHZj7o=", "requires": {"ansi-styles": "^3.2.1", "chalk": "^2.4.1", "cross-spawn": "^6.0.5", "memorystream": "^0.3.1", "minimatch": "^3.0.4", "pidtree": "^0.3.0", "read-pkg": "^3.0.0", "shell-quote": "^1.6.1", "string.prototype.padend": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "cross-spawn": {"version": "6.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cross-spawn/download/cross-spawn-6.0.6.tgz", "integrity": "sha1-MNDvoHEt2361p24ehyG/+vprXVc=", "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "has-flag": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "path-key": {"version": "2.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="}, "semver": {"version": "5.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg="}, "shebang-command": {"version": "1.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="}, "supports-color": {"version": "5.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "requires": {"has-flag": "^3.0.0"}}, "which": {"version": "1.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "requires": {"isexe": "^2.0.0"}}}}, "npm-run-path": {"version": "4.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/npm-run-path/download/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "requires": {"path-key": "^3.0.0"}}, "numerify": {"version": "1.2.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/numerify/download/numerify-1.2.9.tgz", "integrity": "sha1-r0aWux1X+NOXCmFdiwzVPZMr1Vk="}, "object-assign": {"version": "4.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-inspect": {"version": "1.13.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-inspect/download/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM="}, "object-keys": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="}, "object.assign": {"version": "4.1.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==", "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}}, "once": {"version": "1.4.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/onetime/download/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "requires": {"mimic-fn": "^2.1.0"}}, "own-keys": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/own-keys/-/own-keys-1.0.1.tgz", "integrity": "sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==", "requires": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}}, "oxc-resolver": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/oxc-resolver/-/oxc-resolver-6.0.0.tgz", "integrity": "sha512-XbjFKJrpQiVl4XlJE44ly+fNdV5+adm8b/Ax9EIGYpA160PVgYVRUfmdYD1SHOO8z1oZ+CFNZ4/A3EUrNP+/cA==", "dev": true, "requires": {"@oxc-resolver/binding-darwin-arm64": "6.0.0", "@oxc-resolver/binding-darwin-x64": "6.0.0", "@oxc-resolver/binding-freebsd-x64": "6.0.0", "@oxc-resolver/binding-linux-arm-gnueabihf": "6.0.0", "@oxc-resolver/binding-linux-arm64-gnu": "6.0.0", "@oxc-resolver/binding-linux-arm64-musl": "6.0.0", "@oxc-resolver/binding-linux-riscv64-gnu": "6.0.0", "@oxc-resolver/binding-linux-s390x-gnu": "6.0.0", "@oxc-resolver/binding-linux-x64-gnu": "6.0.0", "@oxc-resolver/binding-linux-x64-musl": "6.0.0", "@oxc-resolver/binding-wasm32-wasi": "6.0.0", "@oxc-resolver/binding-win32-arm64-msvc": "6.0.0", "@oxc-resolver/binding-win32-x64-msvc": "6.0.0"}}, "p-limit": {"version": "2.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="}, "parent-module": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "requires": {"callsites": "^3.0.0"}}, "parse-json": {"version": "5.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/parse-json/download/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "path-exists": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-key": {"version": "3.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="}, "path-parse": {"version": "1.0.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "path-type": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-type/download/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs="}, "picocolors": {"version": "0.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-0.2.1.tgz", "integrity": "sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8="}, "picomatch": {"version": "2.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI="}, "pidtree": {"version": "0.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pidtree/download/pidtree-0.3.1.tgz", "integrity": "sha1-7wmsLMBTPfHzJQzPLE02aw0SEUo="}, "pify": {"version": "4.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pify/download/pify-4.0.1.tgz", "integrity": "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="}, "pirates": {"version": "4.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pirates/-/pirates-4.0.6.tgz", "integrity": "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg=="}, "pkg-dir": {"version": "4.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pkg-dir/download/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "requires": {"find-up": "^4.0.0"}}, "possible-typed-array-names": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "integrity": "sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg=="}, "postcss": {"version": "7.0.39", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss/download/postcss-7.0.39.tgz", "integrity": "sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=", "requires": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}}, "postcss-pxtorem": {"version": "5.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss-pxtorem/download/postcss-pxtorem-5.1.1.tgz", "integrity": "sha1-GYpowQ+a0tQjcO9mKZ17MWj4z/o=", "requires": {"postcss": "^7.0.27"}}, "prettier-standalone": {"version": "1.3.1-0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prettier-standalone/download/prettier-standalone-1.3.1-0.tgz", "integrity": "sha1-jDAlSTFscjAVZC5m/UGS22hRTQw="}, "pretty-format": {"version": "29.7.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pretty-format/-/pretty-format-29.7.0.tgz", "integrity": "sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==", "requires": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "dependencies": {"ansi-styles": {"version": "5.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-5.2.0.tgz", "integrity": "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s="}, "react-is": {"version": "18.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="}}}, "private": {"version": "0.1.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/private/download/private-0.1.8.tgz", "integrity": "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8="}, "prompts": {"version": "2.4.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prompts/download/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "requires": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}}, "prop-types": {"version": "15.8.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prop-types/download/prop-types-15.8.1.tgz", "integrity": "sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=", "requires": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "punycode": {"version": "1.4.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/punycode/download/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}, "pure-rand": {"version": "6.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pure-rand/-/pure-rand-6.1.0.tgz", "integrity": "sha512-b<PERSON><PERSON>awvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA=="}, "qs": {"version": "6.14.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qs/download/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "requires": {"side-channel": "^1.1.0"}}, "querystringify": {"version": "2.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/querystringify/download/querystringify-2.2.0.tgz", "integrity": "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y="}, "react-is": {"version": "16.13.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/react-is/download/react-is-16.13.1.tgz", "integrity": "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="}, "read-pkg": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/read-pkg/download/read-pkg-3.0.0.tgz", "integrity": "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=", "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}, "dependencies": {"path-type": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-type/download/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="}}}, "reflect.getprototypeof": {"version": "1.0.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz", "integrity": "sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==", "requires": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}}, "regenerate": {"version": "1.4.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerate/download/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo="}, "regenerate-unicode-properties": {"version": "10.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz", "integrity": "sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==", "requires": {"regenerate": "^1.4.2"}}, "regenerator-runtime": {"version": "0.11.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz", "integrity": "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk="}, "regenerator-transform": {"version": "0.10.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-transform/download/regenerator-transform-0.10.1.tgz", "integrity": "sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0=", "requires": {"babel-runtime": "^6.18.0", "babel-types": "^6.19.0", "private": "^0.1.6"}}, "regexp.prototype.flags": {"version": "1.5.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz", "integrity": "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==", "requires": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}}, "regexpu-core": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regexpu-core/download/regexpu-core-2.0.0.tgz", "integrity": "sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=", "requires": {"regenerate": "^1.2.1", "regjsgen": "^0.2.0", "regjsparser": "^0.1.4"}}, "regjsgen": {"version": "0.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regjsgen/download/regjsgen-0.2.0.tgz", "integrity": "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc="}, "regjsparser": {"version": "0.1.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regjsparser/download/regjsparser-0.1.5.tgz", "integrity": "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=", "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsesc/download/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="}}}, "require-directory": {"version": "2.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="}, "requires-port": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/requires-port/download/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resize-observer-polyfill": {"version": "1.5.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="}, "resolve": {"version": "1.22.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "requires": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-cwd": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve-cwd/download/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "requires": {"resolve-from": "^5.0.0"}}, "resolve-from": {"version": "5.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk="}, "resolve.exports": {"version": "2.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve.exports/-/resolve.exports-2.0.3.tgz", "integrity": "sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A=="}, "rslog": {"version": "1.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/rslog/-/rslog-1.2.3.tgz", "integrity": "sha512-antALPJaKBRPBU1X2q9t085K4htWDOOv/K1qhTUk7h0l1ePU/KbDqKJn19eKP0dk7PqMioeA0+fu3gyPXCsXxQ==", "dev": true}, "rspack-chain": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/rspack-chain/-/rspack-chain-1.2.1.tgz", "integrity": "sha512-RXix83G+5D9ewhmeAUqsJa6l5QmAVCJU4Q3n7D/9Nt1/dBsg/4rvr3fuTGM4Bi55jCFq8z6m0PvsOrRiEMDhQw==", "dev": true, "requires": {"deepmerge": "^4.3.1", "javascript-stringify": "^2.0.1"}}, "runes": {"version": "0.4.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/runes/-/runes-0.4.3.tgz", "integrity": "sha512-K6p9y4ZyL9wPzA+PMDloNQPfoDGTiFYDvdlXznyGKgD10BJpcAosvATKrExRKOrNLgD8E7Um7WGW0lxsnOuNLg=="}, "sacss": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sacss/-/sacss-1.1.0.tgz", "integrity": "sha512-pW8naKibpkZ4KL+hzFivKgXKTXjlLE9qJGmaG3FVSPmT5bKVHoM0WKO7GYQvVHZK2nQsH9d2QITf2e+wdhYB7A=="}, "safe-array-concat": {"version": "1.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safe-array-concat/-/safe-array-concat-1.1.3.tgz", "integrity": "sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==", "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}}, "safe-push-apply": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safe-push-apply/-/safe-push-apply-1.0.0.tgz", "integrity": "sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==", "requires": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}}, "safe-regex-test": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==", "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}}, "semver": {"version": "6.3.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ="}, "set-function-length": {"version": "1.2.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/set-function-length/download/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "requires": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}}, "set-function-name": {"version": "2.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/set-function-name/-/set-function-name-2.0.2.tgz", "integrity": "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==", "requires": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}}, "set-proto": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/set-proto/-/set-proto-1.0.0.tgz", "integrity": "sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==", "requires": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}}, "shallow-clone": {"version": "3.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shallow-clone/download/shallow-clone-3.0.1.tgz", "integrity": "sha1-jymBrZJTH1UDWwH7IwdppA4C76M=", "requires": {"kind-of": "^6.0.2"}}, "shebang-command": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="}, "shell-quote": {"version": "1.8.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shell-quote/-/shell-quote-1.8.3.tgz", "integrity": "sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw=="}, "side-channel": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/side-channel/download/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "requires": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}}, "side-channel-list": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/side-channel-list/download/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "requires": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}}, "side-channel-map": {"version": "1.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/side-channel-map/download/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}}, "side-channel-weakmap": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}}, "signal-exit": {"version": "3.0.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/signal-exit/download/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk="}, "sisteransi": {"version": "1.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sisteransi/download/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0="}, "slash": {"version": "3.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/slash/download/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ="}, "source-map": {"version": "0.6.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}, "source-map-js": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map-js/download/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "dev": true}, "source-map-support": {"version": "0.5.13", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map-support/download/source-map-support-0.5.13.tgz", "integrity": "sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=", "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "spdx-correct": {"version": "3.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/spdx-correct/download/spdx-correct-3.2.0.tgz", "integrity": "sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=", "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz", "integrity": "sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w=="}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.21", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/spdx-license-ids/-/spdx-license-ids-3.0.21.tgz", "integrity": "sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg=="}, "sprintf-js": {"version": "1.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "stack-utils": {"version": "2.0.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stack-utils/download/stack-utils-2.0.6.tgz", "integrity": "sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=", "requires": {"escape-string-regexp": "^2.0.0"}, "dependencies": {"escape-string-regexp": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q="}}}, "stop-iteration-iterator": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz", "integrity": "sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==", "requires": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}}, "string-length": {"version": "4.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string-length/download/string-length-4.0.2.tgz", "integrity": "sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=", "requires": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}}}, "string-width": {"version": "4.2.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}}}, "string.prototype.padend": {"version": "3.1.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string.prototype.padend/-/string.prototype.padend-3.1.6.tgz", "integrity": "sha512-XZpspuSB7vJWhvJc9DLSlrXl1mcA2BdoY5jjnS135ydXqLoqhs96JjDtCkjJEQHvfqZIp9hBuBMgI589peyx9Q==", "requires": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}}, "string.prototype.trim": {"version": "1.2.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz", "integrity": "sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==", "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}}, "string.prototype.trimend": {"version": "1.0.9", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz", "integrity": "sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==", "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}}, "string.prototype.trimstart": {"version": "1.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz", "integrity": "sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==", "requires": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "4.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-bom/download/strip-bom-4.0.0.tgz", "integrity": "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg="}, "strip-final-newline": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-final-newline/download/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="}, "strip-json-comments": {"version": "3.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY="}, "stylis": {"version": "4.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stylis/download/stylis-4.2.0.tgz", "integrity": "sha1-edruAgiWTI/mlaQvz/ysYzohGlE="}, "supports-color": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk="}, "swc-loader": {"version": "0.2.6", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/swc-loader/-/swc-loader-0.2.6.tgz", "integrity": "sha512-9Zi9UP2YmDpgmQVbyOPJClY0dwf58JDyDMQ7uRc4krmc72twNI2fvlBWHLqVekBpPc7h5NJkGVT1zNDxFrqhvg==", "dev": true, "requires": {"@swc/counter": "^0.1.3"}}, "swc-minify-webpack-plugin": {"version": "2.1.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/swc-minify-webpack-plugin/-/swc-minify-webpack-plugin-2.1.2.tgz", "integrity": "sha512-UL8UlHDddcZS5jExTuuX+DAKizdAxFXhmc0G0PB5GDNpYWf3bBDaoMjL7yoeebRnhPCerrN+17qXHk/cVzzzgg==", "dev": true}, "swiper": {"version": "11.2.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/swiper/-/swiper-11.2.8.tgz", "integrity": "sha512-S5FVf6zWynPWooi7pJ7lZhSUe2snTzqLuUzbd5h5PHUOhzgvW0bLKBd2wv0ixn6/5o9vwc/IkQT74CRcLJQzeg=="}, "test-exclude": {"version": "6.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/test-exclude/download/test-exclude-6.0.0.tgz", "integrity": "sha1-BKhphmHYBepvopO2y55jrARO8V4=", "requires": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}}, "tmpl": {"version": "1.0.5", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tmpl/download/tmpl-1.0.5.tgz", "integrity": "sha1-hoPguQK7nCDE9ybjwLafNlGMB8w="}, "to-fast-properties": {"version": "1.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/to-fast-properties/download/to-fast-properties-1.0.3.tgz", "integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc="}, "to-regex-range": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "requires": {"is-number": "^7.0.0"}}, "tslib": {"version": "1.14.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="}, "type-detect": {"version": "4.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/type-detect/download/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw="}, "type-fest": {"version": "0.21.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/type-fest/download/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc="}, "typed-array-buffer": {"version": "1.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz", "integrity": "sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==", "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}}, "typed-array-byte-length": {"version": "1.0.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz", "integrity": "sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==", "requires": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}}, "typed-array-byte-offset": {"version": "1.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz", "integrity": "sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==", "requires": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}}, "typed-array-length": {"version": "1.0.7", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/typed-array-length/-/typed-array-length-1.0.7.tgz", "integrity": "sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==", "requires": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}}, "unbox-primitive": {"version": "1.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unbox-primitive/-/unbox-primitive-1.1.0.tgz", "integrity": "sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==", "requires": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}}, "undici-types": {"version": "7.8.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/undici-types/-/undici-types-7.8.0.tgz", "integrity": "sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw=="}, "unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "integrity": "sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg=="}, "unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=", "requires": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}}, "unicode-match-property-value-ecmascript": {"version": "2.2.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz", "integrity": "sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg=="}, "unicode-property-aliases-ecmascript": {"version": "2.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "integrity": "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w=="}, "update-browserslist-db": {"version": "1.1.3", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "dependencies": {"picocolors": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s="}}}, "url": {"version": "0.11.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/url/-/url-0.11.4.tgz", "integrity": "sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==", "requires": {"punycode": "^1.4.1", "qs": "^6.12.3"}}, "url-parse": {"version": "1.5.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/url-parse/download/url-parse-1.5.10.tgz", "integrity": "sha1-nTwvc2wddd070r5QfcwRHx4uqcE=", "requires": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "uuid": {"version": "8.3.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="}, "v8-to-istanbul": {"version": "9.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "integrity": "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==", "requires": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "vue-demi": {"version": "0.14.10", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg=="}, "walker": {"version": "1.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/walker/download/walker-1.0.8.tgz", "integrity": "sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=", "requires": {"makeerror": "1.0.12"}}, "webpack-merge": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/webpack-merge/-/webpack-merge-6.0.1.tgz", "integrity": "sha512-hXXvrjtx2PLYx4qruKl+kyRSLc52V+cCvMxRjmKwoA+CBbbF5GfIBtR6kCvl0fYGqTUPKB+1ktVmTHqMOzgCBg==", "dev": true, "requires": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.1"}}, "webworkify": {"version": "1.5.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/webworkify/download/webworkify-1.5.0.tgz", "integrity": "sha1-c0rYendN5uvdVG4dPgJ9pbj0pCw="}, "which": {"version": "2.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "requires": {"isexe": "^2.0.0"}}, "which-boxed-primitive": {"version": "1.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "integrity": "sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==", "requires": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}}, "which-builtin-type": {"version": "1.2.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which-builtin-type/-/which-builtin-type-1.2.1.tgz", "integrity": "sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==", "requires": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}}, "which-collection": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which-collection/-/which-collection-1.0.2.tgz", "integrity": "sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==", "requires": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}}, "which-typed-array": {"version": "1.1.19", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==", "requires": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}}, "wildcard": {"version": "2.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wildcard/download/wildcard-2.0.1.tgz", "integrity": "sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=", "dev": true}, "wrap-ansi": {"version": "7.0.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wrap-ansi/download/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}}}, "wrappy": {"version": "1.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write-file-atomic": {"version": "4.0.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/write-file-atomic/download/write-file-atomic-4.0.2.tgz", "integrity": "sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=", "requires": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}}, "y18n": {"version": "5.0.8", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/y18n/download/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU="}, "yallist": {"version": "3.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="}, "yaml": {"version": "1.10.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yaml/download/yaml-1.10.2.tgz", "integrity": "sha1-IwHF/78StGfejaIzOkWeKeeSDks="}, "yargs": {"version": "17.7.2", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yargs/download/yargs-17.7.2.tgz", "integrity": "sha1-mR3zmspnWhkrgW4eA2P5110qomk=", "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yargs-parser/download/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU="}, "yocto-queue": {"version": "0.1.0", "resolved": "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yocto-queue/download/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs="}}}