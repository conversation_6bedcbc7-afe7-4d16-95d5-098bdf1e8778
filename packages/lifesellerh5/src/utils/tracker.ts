import { push } from 'shared/tracker'
import tracker from '@xhs/protobuf-pages-lifeseller-tracker/tracker_template'
import { eaglet } from '@xhs/launcher-plugin-eaglet'

function pushTracker(...args) {
  try {
    // @ts-ignore
    eaglet.push(...args)
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(e)
  }
}

export interface UploadAttributes {
  isSuccess: boolean
  fieldName?: string
  errMessage?: string
}

// 上传提交(lifesellerH5_common_upload_submit_button)
export const trackerCommonUploadSubmit = (attributes:UploadAttributes) => {
  push({
    moduleKey: 'common_upload_submit_button',
    attributes,
  })
}

// 上传提交(lifesellerH5_pass_upload_submit_button)
export const trackerPassUploadSubmit = (attributes:UploadAttributes) => {
  push({
    moduleKey: 'pass_upload_submit_button',
    attributes,
  })
}

// 上传提交(lifesellerH5_identity_upload_submit_button)
export const trackerIdentityUploadSubmit = (attributes:UploadAttributes) => {
  push({
    moduleKey: 'identity_upload_submit_button',
    attributes,
  })
}

// 探店博主详情页
export interface TrackerKolProfileAttributes {
  source: string
  channelTabName?: string
  cityName: string
  bloggerId: string
  sortType?: string
  noteId?: string
  noteType?: string
  objectPosition?: number
}

export const trackerKolProfileImpression = (attributes:TrackerKolProfileAttributes) => {
  pushTracker(tracker[81736](attributes))
}

export const trackerKolProfileNoteSortClick = (attributes:TrackerKolProfileAttributes) => {
  pushTracker(tracker[81737](attributes))
}

export const trackerKolProfileNoteImpression = (attributes:TrackerKolProfileAttributes) => {
  pushTracker(tracker[81739](attributes))
}

export const trackerKolProfileNoteClick = (attributes:TrackerKolProfileAttributes) => {
  pushTracker(tracker[81740](attributes))
}

export const trackerKolProfileSubmitClick = (attributes:TrackerKolProfileAttributes) => {
  pushTracker(tracker[81741](attributes))
}
