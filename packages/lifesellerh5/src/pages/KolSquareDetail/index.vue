<template>
  <div class="kol-square-detail">
    <NavBar
      title="博主详情"
      :container-styles="{
        background: isNavbarBordered ? 'var(--bg2)' : 'var(--bg0)',
        borderBottom: (isNavbarBordered && !isTabsFixed) ? '0.5px solid var(--separator)' : 'none',
      }"
    >
      <template #right>
        <Icon :icon="InfoSvg" @click="handleInfoClick" />
      </template>
    </NavBar>
    <Alert
      title="以下为博主近90天数据"
      :show="showAlert"
      confirm-text="我知道了"
      :show-cancel-button="false"
      footer-type="text"
      @Mask="() => showAlert = false"
      @confirm="showAlert = false"
    />
    <div class="content">
      <BaseInfoSkeleton v-if="baseInfoLoading" style="margin: 0px 16px" />
      <KolInfoCard v-else ref="kolInfoCard" :kol-info="kolBaseInfo" style="margin: 0px 16px 20px" />
      <div
        ref="tabsContainer"
        :class="['tabs-container', { 'fixed': isTabsFixed }]"
        :style="{
          top: statusBarHeight + 44 + 'px',
          background: isTabsFixed ? 'var(--bg2)' : 'var(--bg0)',
        }"
      >
        <TabsRed v-model="activeTab" :tabs="tabs" :style="tabsStyle" @change="handleChange" />
      </div>
      <div v-if="isTabsFixed" class="tabs-placeholder"></div>
      <div class="tab-content">
        <div v-if="noteData.stat_data?.length && noteData?.note_data?.length" class="data-card">
          <div
            v-for="item in (noteData.stat_data?.filter(item => item.value) ?? [])"
            :key="item.name"
            class="data-card-item"
          >
            <div class="data-card-item-value">
              <span class="value">{{ formatCount(item.value ?? 0) }}</span>
              <Tag v-if="item.tag" size="mini" style="text-wrap: nowrap;">{{ item.tag }}</Tag>
            </div>
            <div class="data-card-item-label">{{ item.name }}</div>
          </div>
        </div>
        <div class="note-list">
          <div v-if="noteData?.note_data?.length" class="sort-btn-list">
            <div
              v-for="sortBtn in sortBtns[activeTab]"
              :key="sortBtn.value"
              class="sort-btn"
              :class="{ active: sortBtn.value === activeSortBtn }"
              @click="handleSortBtnClick(sortBtn.value as NoteRankTypeEnum)"
            >
              {{ sortBtn.name }}
            </div>
          </div>
          <DataListSkeleton v-if="noteDataLoading && !noteData?.note_data?.length" />
          <Empty v-if="!noteData?.note_data?.length && !noteDataLoading" style="margin: 0 auto" description="近90天没有笔记" />
          <div v-if="activeTab === DataTypeEnum.VISIT_PERFORMANCE" class="note-list-content">
            <div
              v-for="(note, index) in noteData?.note_data ?? []"
              :key="note.note_id"
              class="note-list-item"
            >
              <VisitPerformanceNoteCard
                :note="note"
                @on-mount="() => handleNoteMount(note, index)"
                @click="() => handleNoteClick(note, index)"
              />
              <div v-if="index !== (noteData?.note_data?.length ?? 0) - 1" class="divider" :style="{ width: dividerWidth + 'px' }"></div>
            </div>
          </div>
          <div v-if="activeTab === DataTypeEnum.CONTENT_PERFORMANCE" class="note-list-content">
            <div
              v-for="(note, index) in noteData?.note_data ?? []"
              :key="note.note_id"
              class="note-list-item"
            >
              <ContentPerformanceNoteCard
                :note="note"
                @on-mount="() => handleNoteMount(note, index)"
                @click="() => handleNoteClick(note, index)"
              />
              <div v-if="index !== (noteData?.note_data?.length ?? 0) - 1" class="divider" :style="{ width: dividerWidth + 'px' }"></div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="noteData?.note_data?.length" class="bottom-tip" :style="{ marginBottom: footerHeight + 'px' }">- 到底了 -</div>
      <FooterWrap style="border-top: 0.5px solid var(--separator); padding: 8PX 16PX; padding-bottom: 41.5PX; height: auto">
        <Button type="primary" variant="fill" size="large" block @click="handleSubmitClick">发送邀约</Button>
      </FooterWrap>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref, onMounted, watch, onUnmounted, computed
  } from 'vue'
  import {
    Button, Empty, Tag, useStatusBarHeight, Alert, Icon,
  } from '@xhs/reds-h5-next'
  import { useRoute } from 'vue-router'
  import { tryCatch } from 'shared/utils'
  import { Toast } from '@xhs/riant'
  import { once } from 'lodash-es'
  import { invoke } from '@xhs/ozone-schema'
  import { formatCount } from 'shared/utils/number'
  import NavBar from '~/components-next/NavBar.vue'
  import InfoSvg from '~/assets/svg/info.svg'
  import TabsRed from '~/components/Tabs-Red.vue'
  import FooterWrap from '~/components/FooterWrap.vue'
  import KolInfoCard from './components/KolInfoCard.vue'
  import { BaseInfoSkeleton, DataListSkeleton } from './components/Skeleton'
  import VisitPerformanceNoteCard from './components/VisitPerformanceNoteCard.vue'
  import ContentPerformanceNoteCard from './components/ContentPerformanceNoteCard.vue'
  import {
    getKolSquareDetailBaseInfo,
    KolSquareDetailData,
    getKolSquareDetailNoteData,
    NoteData,
    DataTypeEnum,
    NoteRankTypeEnum,
    NoteRankTypeMap,
    DataTypeMap,
  } from './services'
  import {
    trackerKolProfileImpression,
    trackerKolProfileNoteSortClick,
    trackerKolProfileSubmitClick,
    trackerKolProfileNoteImpression,
    trackerKolProfileNoteClick,
  } from '~/utils/tracker'

  invoke('alwaysBounceIOS', {
    bounces: false,
  })

  const showAlert = ref(false)
  const handleInfoClick = () => {
    showAlert.value = true
  }

  const route = useRoute()
  const kolBaseInfo = ref<KolSquareDetailData>({})

  const commonTrackerAttributes = {
    source: route.query.source as string,
    cityName: decodeURIComponent(route.query.city_name as string),
    bloggerId: route.query.kol_user_id as string,
  }

  const baseInfoLoading = ref(false)
  async function getKolBaseInfo() {
    baseInfoLoading.value = true
    const [res, err] = await tryCatch(() => getKolSquareDetailBaseInfo({
      kol_user_id: route.query.kol_user_id as string,
      city_info: {
        name: decodeURIComponent(route.query.city_name as string),
        code: route.query.city_code as string,
      },
    }))
    baseInfoLoading.value = false
    if (res && !err) {
      kolBaseInfo.value = res
    } else {
      Toast(err?.message || '获取博主信息失败')
    }
  }
  getKolBaseInfo()

  const noteData = ref<KolSquareDetailData>({})
  const activeTab = ref(DataTypeEnum.VISIT_PERFORMANCE)

  const handleChange = (value: DataTypeEnum) => {
    activeTab.value = value
    if (value === DataTypeEnum.VISIT_PERFORMANCE) {
      activeSortBtn.value = NoteRankTypeEnum.GMV
    } else {
      activeSortBtn.value = NoteRankTypeEnum.READ;
      (once(() => {
        trackerKolProfileImpression({
          ...commonTrackerAttributes,
          channelTabName: DataTypeMap[activeTab.value],
        })
      }))()
    }
  }

  const tabs = [
    {
      name: DataTypeMap[DataTypeEnum.VISIT_PERFORMANCE],
      value: DataTypeEnum.VISIT_PERFORMANCE,
    },
    {
      name: DataTypeMap[DataTypeEnum.CONTENT_PERFORMANCE],
      value: DataTypeEnum.CONTENT_PERFORMANCE,
    },
  ]

  const activeSortBtn = ref(NoteRankTypeEnum.GMV)
  const noteDataLoading = ref(false)
  async function getNoteData() {
    noteDataLoading.value = true
    const [res, err] = await tryCatch(() => getKolSquareDetailNoteData({
      kol_user_id: route.query.kol_user_id as string,
      note_rank_type: activeSortBtn.value,
      data_type: activeTab.value,
      city_info: {
        code: route.query.city_code as string,
        name: decodeURIComponent(route.query.city_name as string),
      },
    }))
    noteDataLoading.value = false
    if (res && !err) {
      noteData.value = res
    } else {
      Toast(err?.message || '获取笔记信息失败')
    }
  }
  watch(() => activeTab.value, () => {
    getNoteData()
  }, {
    immediate: true,
  })

  const sortBtns = {
    [DataTypeEnum.VISIT_PERFORMANCE]: [
      {
        name: NoteRankTypeMap[NoteRankTypeEnum.GMV],
        value: NoteRankTypeEnum.GMV,
      },
      {
        name: NoteRankTypeMap[NoteRankTypeEnum.IMPRESSION],
        value: NoteRankTypeEnum.IMPRESSION,
      },
      {
        name: NoteRankTypeMap[NoteRankTypeEnum.PUBTIME],
        value: NoteRankTypeEnum.PUBTIME,
      },
    ],
    [DataTypeEnum.CONTENT_PERFORMANCE]: [
      {
        name: NoteRankTypeMap[NoteRankTypeEnum.READ],
        value: NoteRankTypeEnum.READ,
      },
      {
        name: NoteRankTypeMap[NoteRankTypeEnum.ENGAGEMENT],
        value: NoteRankTypeEnum.ENGAGEMENT,
      },
      {
        name: NoteRankTypeMap[NoteRankTypeEnum.PUBTIME],
        value: NoteRankTypeEnum.PUBTIME,
      },
    ],
  }

  const handleSortBtnClick = (value: NoteRankTypeEnum) => {
    activeSortBtn.value = value
    trackerKolProfileNoteSortClick({
      ...commonTrackerAttributes,
      sortType: NoteRankTypeMap[value],
      channelTabName: DataTypeMap[activeTab.value],
    })
    getNoteData()
  }

  const handleSubmitClick = () => {
    trackerKolProfileSubmitClick({
      ...commonTrackerAttributes,
    })
    window.open(`xhsdiscover://rn/lancer-bk/bk-match/business/pick-plan?biz_scene=SELLER_TOOL&target_user_id=${route.query.kol_user_id}&statusbar_transparent=true&shadowViewFullScreen=true&background_transparent_v2=true&needNewRootNavigation=true`, '_blank')
  }

  const handleNoteMount = (note: NoteData, index: number) => {
    trackerKolProfileNoteImpression({
      ...commonTrackerAttributes,
      noteId: note.note_id,
      noteType: note.note_type === '1' ? 'short_note' : 'video_note',
      objectPosition: index,
      sortType: NoteRankTypeMap[activeSortBtn.value],
      channelTabName: DataTypeMap[activeTab.value],
    })
  }

  const handleNoteClick = (note: NoteData, index: number) => {
    trackerKolProfileNoteClick({
      ...commonTrackerAttributes,
      noteId: note.note_id,
      noteType: note.note_type === '1' ? 'short_note' : 'video_note',
      objectPosition: index,
      sortType: NoteRankTypeMap[activeSortBtn.value],
      channelTabName: DataTypeMap[activeTab.value],
    })
    if (note.jump_link) {
      window.open(note.jump_link, '_blank')
    }
  }

  const footerHeight = ref(0)
  const dividerWidth = ref(0)

  const tabsContainer = ref<HTMLElement | null>(null)
  const tabsOffsetTop = ref(0)
  const isTabsFixed = ref(false)
  const kolInfoCard = ref<HTMLElement | null>(null)
  const isNavbarBordered = ref(false)

  const statusBarHeight = useStatusBarHeight()

  const tabsStyle = computed(() => ({
    borderBottom: isTabsFixed.value ? '0.5px solid var(--separator)' : 'none',
  }))

  const handleScroll = () => {
    if (!tabsContainer.value) return

    if (window.scrollY >= tabsOffsetTop.value - 44 + 5) {
      isTabsFixed.value = true
    } else {
      isTabsFixed.value = false
    }

    // 简化判断逻辑，只要滚动超过12px就添加边框
    isNavbarBordered.value = window.scrollY > 12
  }

  onMounted(() => {
    footerHeight.value = document.querySelector('.action-footer')?.clientHeight || 0
    trackerKolProfileImpression({
      ...commonTrackerAttributes,
      channelTabName: DataTypeMap[activeTab.value],
    })
    const noteList = document.querySelector('.note-list')
    if (noteList) {
      // 使用rem单位进行响应式计算
      // 假设1rem = 16px，将固定像素值转换为rem
      const remUnit = 16 // 基准单位
      const paddingRem = 16 / remUnit // 1rem
      const avatarWidthRem = 64 / remUnit // 4rem
      const marginRem = 12 / remUnit // 0.75rem
      dividerWidth.value = noteList.clientWidth - (paddingRem + avatarWidthRem + marginRem) * remUnit
    }

    // 设置tabs的初始位置
    if (tabsContainer.value) {
      tabsOffsetTop.value = tabsContainer.value.offsetTop
    }
    // 添加滚动监听
    window.addEventListener('scroll', handleScroll)

    const style = document.createElement('style')
    style.textContent = `
      html {
        overflow: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }
      html::-webkit-scrollbar {
        display: none;
      }
    `
    document.head.appendChild(style)
  })

  onUnmounted(() => {
    // 移除滚动监听
    window.removeEventListener('scroll', handleScroll)
  })
</script>

<style scoped lang="stylus">
@import '@xhs/water-kaipingyidongBduan/index.css'
.kol-square-detail
  background: var(--bg0)
  min-height: 100vh
  overflow: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar
    display: none;
  .content
    padding: 12px 0px

  .tabs-container
    width: 100%
    transition: all 0.3s
    padding: 0 16px
    z-index: 10
    &.fixed
      width: 100%
      position: fixed
      left: 0
      right: 0
      z-index: 100

  .tabs-placeholder
    height: 52px // TabsRed的高度 + margin

  .tab-content
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 0 16px
    margin-top: 8px
    .data-card
      width: 100%
      // height: 100px
      background: var(--bg2)
      border-radius: 12px
      padding 12px 16px
      display: grid
      grid-template-columns: repeat(4, 1fr)
      grid-row-gap: 16px
      .data-card-item
        display: flex;
        align-items: center;
        flex-direction: column;
        gap 2px
        .data-card-item-value
          display: flex;
          align-items: center;
          gap: 4px;
          overflow: hidden;
          color: var(--paragraph);
          text-overflow: ellipsis;
          font-size: var(--t2-font-size);
          font-weight: var(--number-font-weight);
          line-height: var(--t2-line-height);
        .data-card-item-label
          overflow: hidden;
          text-wrap: nowrap;
          color: var(--description);
          text-overflow: ellipsis;
          font-family: "PingFang SC";
          font-size: var(--c3-font-size);
          font-style: normal;
          font-weight: var(--c3-font-weight);
          line-height: var(--c3-line-height);
    .note-list
      width: 100%
      background: var(--bg2)
      border-radius: 12px
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding 12px 16px
      .note-list-content
        width: 100%
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative
        .note-list-item
          width: 100%
          display: flex;
          flex-direction: column;
          align-items flex-end
        .divider
          border-bottom: 0.5PX solid rgba(0, 0, 0, 0.08)
          margin 12px 0
          transform: translateX(16PX)
      .sort-btn-list
        display: flex;
        align-items: center;
        gap: 12px;
        .sort-btn
          border-radius: 6px;
          border: 0.5px solid var(--separator2);
          display: flex;
          height: 28px;
          padding: 5px 10px;
          justify-content: center;
          align-items: center;
          color: var(--paragraph);
          font-size: var(--c2-font-size);
          font-style: normal;
          font-weight: var(--c2-font-weight);
          line-height: var(--c2-line-height)
          &.active
            color: #FF2442
            font-size: var(--c2-emphasized-font-size);
            font-weight: var(--c2-emphasized-font-weight);
            line-height: var(--c2-emphasized-line-height);
            border: 0.5px solid #FF2442
  .bottom-tip
    height 64px
    display: flex;
    align-items: center;
    justify-content: center
    color: var(--description);
    text-align: center;
    font-family: "PingFang SC";
    font-size: var(--c2-font-size);
    font-style: normal;
    font-weight: var(--c2-font-weight);
    line-height: var(--c2-line-height);
</style>
