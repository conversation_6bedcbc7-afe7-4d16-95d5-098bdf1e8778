import { http } from '@xhs/launcher'
import { env } from 'shared/config/env.config'

const hostMap = {
  test: 'http://www-square.sl.beta.xiaohongshu.com',
  development: 'http://www-square.sl.beta.xiaohongshu.com',
  prerelease: 'http://www-square.sl.beta.xiaohongshu.com',
  production: 'https://www.xiaohongshu.com',
}

const host = hostMap[env] || 'https://www.xiaohongshu.com'

// KOL基础信息类型定义
export interface KolDataInfo {
  fans_distribution?: string[]
  fans_count?: string
  median_read_count?: string
  gmv?: string
  categories?: string[]
  history_collaboration?: string[]
}

export interface KolBaseInfo {
  user_name?: string
  user_id?: string
  xhs_id?: string
  gender?: string
  avatar?: string
  jump_link?: string
  tags?: string[]
  city_name?: string
}

// 统计数据类型定义
export interface StatData {
  name?: string
  value?: string | number
  tag?: string
}

// 笔记数据类型定义
export interface NoteData {
  note_id?: string
  jump_link?: string
  like_count?: number
  collect_count?: number
  comment_count?: number
  gmv?: string
  impression?: string
  image?: string
  read_count?: string
  content?: string
  pub_date?: string
  poi_name?: string
  poi_category_name?: string
  note_type?: string
}

// 整体数据结构
export interface KolSquareDetailData {
  kol_base_info?: KolBaseInfo
  stat_data?: StatData[]
  note_data?: NoteData[]
  kol_data_info?: KolDataInfo
}

export enum DataTypeEnum {
  VISIT_PERFORMANCE = 1,
  CONTENT_PERFORMANCE = 2,
}
export const DataTypeMap = {
  [DataTypeEnum.VISIT_PERFORMANCE]: '探店表现',
  [DataTypeEnum.CONTENT_PERFORMANCE]: '内容表现',
}

export enum NoteRankTypeEnum {
  ENGAGEMENT = 5,
  GMV = 3,
  IMPRESSION = 4,
  PUBTIME = 2,
  READ = 1,
}
export const NoteRankTypeMap = {
  [NoteRankTypeEnum.GMV]: '帮店成交最高',
  [NoteRankTypeEnum.IMPRESSION]: '帮店曝光最高',
  [NoteRankTypeEnum.PUBTIME]: '最新发布',
  [NoteRankTypeEnum.READ]: '阅读最高',
  [NoteRankTypeEnum.ENGAGEMENT]: '互动最高',
}

export const getKolSquareDetailBaseInfo = (params: {
  kol_user_id: string
  city_info: {
    name: string
    code: string
  }
}) => http.post<KolSquareDetailData>(`${host}/api/v2/outdoor/campaign/bk/kol_info`, params)

export const getKolSquareDetailNoteData = (params: {
  kol_user_id: string
  city_info: {
    name: string
    code: string
  }
  note_rank_type: NoteRankTypeEnum
  data_type: DataTypeEnum
}) => http.post<KolSquareDetailData>(`${host}/api/v2/outdoor/campaign/bk/kol_detail_data`, params)
