<template>
  <span v-if="!values?.length" style="color: var(--separator)">-</span>
  <div v-else style="display: flex; align-items: center; flex-wrap: wrap;">
    <div v-for="(value, index) in values" :key="index">
      <span>{{ value }}</span>
      <span v-if="index < values.length - 1" style="color: var(--separator); margin: 0 6px">|</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps({
    values: {
      type: Array as () => string[],
      default: () => []
    }
  })
</script>
