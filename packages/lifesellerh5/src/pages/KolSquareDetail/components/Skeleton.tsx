import { SkeletonItem } from '@xhs/reds-h5-next'

export const BaseInfoSkeleton = () => <div style="display: flex; align-items: start; gap: 12px; width: 100%; background-color: #fff; padding: 16px; border-radius: 12px;">
  <SkeletonItem type="avatar" containerStyle={{ width: '64px', height: '64px', borderRadius: '64px' }} />
  <div style="display: flex; flex-direction: column; gap: 12px; flex: 1">
    <SkeletonItem type="paragraph" containerStyle={{ width: '80px', height: '26px' }} />
    <SkeletonItem type="paragraph" containerStyle={{ width: '250px', height: '48px' }} />
    <div style="display: flex; flex-direction: column; gap: 6px">
      <SkeletonItem type="paragraph" containerStyle={{ width: '200px', height: '18px' }} />
      <SkeletonItem type="paragraph" containerStyle={{ width: '200px', height: '18px' }} />
      <SkeletonItem type="paragraph" containerStyle={{ width: '200px', height: '18px' }} />
      <SkeletonItem type="paragraph" containerStyle={{ width: '200px', height: '18px' }} />
    </div>
  </div>
</div>

export const DataListSkeleton = () => {
  const item = <div style="display: flex; flex-direction: column; gap: 12px; width: 100%;">
    <div style="display: flex; gap: 12px;">
      <SkeletonItem type="paragraph" containerStyle={{ width: '64px', height: '85px' }} />
      <div style="display: flex; flex-direction: column; justify-content: space-between; flex: 1;">
        <SkeletonItem type="paragraph" containerStyle={{ width: '100%', height: '22px' }} />
        <div style="display: flex; flex-direction: column; gap: 12px;">
          <SkeletonItem type="paragraph" containerStyle={{ width: '50%', height: '16px' }} />
          <div style="display: flex; justify-content: space-between;">
            <SkeletonItem type="paragraph" containerStyle={{ width: '70%', height: '18px' }} />
            <SkeletonItem type="paragraph" containerStyle={{ width: '20%', height: '18px' }} />
          </div>
        </div>
      </div>
    </div>
  </div>
  return <div style="display: flex; flex-direction: column; gap: 24px; width: 100%;">
    <item />
    <item />
  </div>
}
