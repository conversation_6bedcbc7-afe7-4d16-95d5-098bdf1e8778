<template>
  <div class="kol-info-card">
    <div class="top">
      <div class="left-avatar" @click="handleAvatarClick">
        <img :src="kolInfo.kol_base_info?.avatar" />
      </div>
      <div class="right-info">
        <div class="name">
          {{ kolInfo.kol_base_info?.user_name }}
          <Icon :icon="kolInfo.kol_base_info?.gender === 'male' ? man : woman" style="display: inline-block; width: 18px; height: 18px; margin-left: 2px;" />
        </div>
        <div class="tags">
          <Tag
            v-for="tag in kolInfo.kol_base_info?.tags"
            :key="tag"
            size="small"
          >
            {{ tag }}
          </Tag>
        </div>
        <div class="extra-info">
          <div v-if="kolInfo.kol_base_info?.city_name" class="extra-info-item">常驻城市：{{ kolInfo.kol_base_info?.city_name }}</div>
          <div v-if="kolInfo?.kol_data_info?.fans_distribution?.length" class="extra-info-item">粉丝分布：<ValueList :values="kolInfo?.kol_data_info?.fans_distribution" /></div>
          <div v-if="kolInfo?.kol_data_info?.categories?.length" class="extra-info-item">笔记类目：<ValueList :values="kolInfo?.kol_data_info?.categories" /></div>
          <div v-if="kolInfo?.kol_data_info?.history_collaboration?.length" class="extra-info-item">历史合作：<ValueList :values="kolInfo?.kol_data_info?.history_collaboration" /></div>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="bottom">
      <div
        v-for="item in (kolInfo.stat_data?.filter(item => item.value) ?? [])"
        :key="item.value"
        class="data-item"
      >
        <div class="data-item-value">{{ item.value }}</div>
        <div class="data-item-label">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
  import { Tag, Icon } from '@xhs/reds-h5-next'
  import man from '~/assets/svg/man.svg'
  import woman from '~/assets/svg/woman.svg'
  import { KolSquareDetailData } from '../services'
  import ValueList from './ValueList.vue'

  const props = defineProps<{
    kolInfo: KolSquareDetailData
  }>()

  const handleAvatarClick = () => {
    if (props.kolInfo.kol_base_info?.jump_link) {
      window.open(props.kolInfo.kol_base_info?.jump_link, '_blank')
    }
  }

</script>

<style scoped lang="stylus">
.text-ellipsis
  overflow: hidden
  text-overflow: ellipsis
.pingfang-font
  font-family: "PingFang SC"
  font-style: normal
.kol-info-card
  display: flex
  padding: 16px 16px 12px 16px
  flex-direction: column
  align-items: flex-start
  align-self: stretch
  border-radius: 12px
  background: var(--bg2)
  .top
    display: flex
    align-items: flex-start
    gap: 12px
    .left-avatar img
      width: 64PX
      height: 64PX
      border-radius: 64PX
      border: 0.5px solid var(--separator)
    .right-info
      display: flex
      flex-direction: column
      align-items: flex-start
      align-self: stretch
      .name
        @extends .text-ellipsis
        @extends .pingfang-font
        display: flex
        align-items: center
        color: var(--title)
        font-size: var(--t1-font-size)
        font-weight: var(--t1-font-weight)
        line-height: var(--t1-line-height)
        margin-bottom: 4px
      .tags
        display: flex
        align-items: flex-start
        gap: 8px
        align-self: stretch
        flex-wrap: wrap
        margin-bottom: 12px
        max-height: 48px
        overflow: hidden
      .extra-info
        display: flex
        flex-direction: column
        align-items: flex-start
        gap: 6px
        .extra-info-item
          @extends .text-ellipsis
          @extends .pingfang-font
          display: flex
          align-items: flex-start
          text-wrap: nowrap
          color: var(--paragraph)
          font-size: var(--c2-font-size)
          font-weight: var(--c2-font-weight)
          line-height: var(--c2-line-height)
  .divider
    width: 100%
    height: 0.5PX
    background: var(--separator)
    margin: 12px 0
  .bottom
    display: flex
    align-items: center
    width: 100%
    .data-item
      display: flex
      flex-direction: column
      align-items: center
      flex: 1
      .data-item-value
        @extends .text-ellipsis
        color: var(--paragraph)
        font-size: var(--t1-font-size)
        font-weight: var(--number-font-weight)
        line-height: var(--t1-line-height)
      .data-item-label
        @extends .text-ellipsis
        @extends .pingfang-font
        color: var(--description)
        font-size: var(--c2-font-size)
        font-weight: var(--c2-font-weight)
        line-height: var(--c2-line-height)
</style>
