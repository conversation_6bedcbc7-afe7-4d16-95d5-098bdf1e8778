<template>
  <div class="visit-performance-note-card">
    <div class="left-img">
      <img :src="note.image">
      <div class="view-number">
        <Icon :icon="viewF" :size="12" />
        <div class="number">{{ formatCount(note.read_count ?? 0) }}</div>
      </div>
    </div>
    <div class="right-content">
      <div class="title">{{ note.content }}</div>
      <div ref="infoRef" class="info">
        <div class="info-content">
          <div class="address" :style="{ width: addressWidth + 'px' }">
            <span v-if="note.poi_category_name">{{ note.poi_category_name ?? '' }}</span>
            <span v-if="note.poi_category_name" style="color: var(--separator); margin: 0 6px">|</span>
            <span>{{ note.poi_name ?? '' }}</span>
          </div>
          <div class="performance">
            <ValueList :values="[`帮店成交 ${formatCount(note.gmv ?? 0)}`, `帮店曝光${formatCount(note.impression ?? 0)}`]" />
          </div>
        </div>
        <div class="time">{{ note.pub_date }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { Icon } from '@xhs/reds-h5-next'
  import { formatCount } from 'shared/utils/number'
  import viewF from '~/assets/svg/view_f.svg'
  import { NoteData } from '../services'
  import ValueList from './ValueList.vue'

  defineProps<{
    note: NoteData
  }>()

  const emit = defineEmits(['onMount'])
  const infoRef = ref<HTMLDivElement>()

  const addressWidth = ref(0)
  onMounted(() => {
    emit('onMount')
    if (infoRef.value?.clientWidth) {
      addressWidth.value = infoRef.value?.clientWidth - 48 || 0
    }
  })
</script>

<style scoped lang="stylus">
.visit-performance-note-card
  display: flex
  align-items: flex-start
  gap: 12px
  height: 85PX
  box-sizing: border-box
  width: 100%
  .left-img
    position: relative
    height: 85PX
    img
      width: 64PX
      height: 85PX
      border-radius: 6px
      flex-shrink: 0
    .view-number
      display: flex
      align-items: center
      gap: 2px
      position: absolute
      left: 4px
      bottom: 4px
      background: rgba(48, 48, 52, 0.50)
      border-radius: 50px
      padding: 2px 4px
      font-size 10px
      font-weight 500
      line-height 14px
      color #fff
  .right-content
    display: flex
    flex-direction: column
    justify-content: space-between
    flex: 1
    height: 100%
    min-width: 0
    max-width: calc(100% - 76px) // 64px图片宽度 + 12px间距
    .title
      font-family: "PingFang SC"
      font-size: var(--b2-loose-font-size)
      font-weight: var(--b2-loose-font-weight)
      line-height: var(--b2-loose-line-height)
      color: var(--title)
      overflow: hidden
      text-overflow: ellipsis
      display: -webkit-box
      -webkit-line-clamp: 1
      -webkit-box-orient: vertical
    .info
      display: flex
      justify-content: space-between
      align-items: flex-end
      width: 100%
      .info-content
        flex: 1
        min-width: 0
        overflow: hidden
        display: flex
        flex-direction: column
        gap 6PX
        .address, .performance
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis
          color: var(--paragraph)
          font-family: "PingFang SC"
          font-size: var(--c2-font-size)
          font-weight: var(--c2-font-weight)
          line-height: var(--c2-line-height)
      .time
        flex-shrink: 0
        margin-left: 6px
        color: var(--paragraph)
        text-align: right
        font-family: "PingFang SC"
        font-size: var(--c2-font-size)
        font-weight: var(--c2-font-weight)
        line-height: var(--c2-line-height)
</style>
