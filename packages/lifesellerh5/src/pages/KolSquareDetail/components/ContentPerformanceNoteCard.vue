<template>
  <div class="visit-performance-note-card">
    <div class="left-img">
      <img :src="note?.image">
      <div class="view-number">
        <Icon :icon="viewF" :size="12" />
        <div class="number">{{ formatCount(note?.read_count ?? 0) }}</div>
      </div>
    </div>
    <div class="right-content">
      <div class="title">{{ note?.content }}</div>
      <div class="info">
        <div class="info-content">
          <div class="icon-item">
            <Icon :icon="likeF" :size="16" />
            <div class="number">{{ formatCount(note?.like_count ?? 0) }}</div>
          </div>
          <div class="icon-item">
            <Icon :icon="collectF" :size="16" />
            <div class="number">{{ formatCount(note?.collect_count ?? 0) }}</div>
          </div>
          <div class="icon-item">
            <Icon :icon="commentF" :size="16" />
            <div class="number">{{ formatCount(note?.comment_count ?? 0) }} </div>
          </div>
        </div>
        <div class="time">
          {{ note?.pub_date }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue'
  import { Icon } from '@xhs/reds-h5-next'
  import { formatCount } from 'shared/utils/number'
  import viewF from '~/assets/svg/view_f.svg'
  import likeF from '~/assets/svg/like.svg'
  import commentF from '~/assets/svg/chat.svg'
  import collectF from '~/assets/svg/collect.svg'
  import { NoteData } from '../services'

  defineProps<{
    note: NoteData
  }>()

  const emit = defineEmits(['onMount'])

  onMounted(() => {
    emit('onMount')
  })

</script>

<style scoped lang="stylus">
.visit-performance-note-card
  display: flex
  align-items: flex-start
  gap: 12px
  height: 85PX
  box-sizing: border-box
  width: 100%
  .left-img
    position: relative
    height: 85PX
    img
      width: 64PX
      height: 85PX
      border-radius: 6px
      flex-shrink: 0
    .view-number
      display: flex
      align-items: center
      gap: 2px
      position: absolute
      left: 4px
      bottom: 4px
      background: rgba(48, 48, 52, 0.50)
      border-radius: 50px
      padding: 2px 4px
      font-size 10px
      font-weight 500
      line-height 14px
      color #fff
  .right-content
    display: flex
    flex-direction: column
    justify-content: space-between
    flex: 1
    height: 100%
    min-width: 0 // 关键属性
    max-width: calc(100% - 76px) // 64px图片宽度 + 12px间距
    .title
      font-family: "PingFang SC"
      font-size: var(--b2-loose-font-size)
      font-weight: var(--b2-loose-font-weight)
      line-height: var(--b2-loose-line-height)
      color: var(--title)
      display: -webkit-box
      -webkit-line-clamp: 2
      -webkit-box-orient: vertical
      white-space: normal
      overflow: hidden
      text-overflow: ellipsis
      width: 100%
    .info
      display: flex
      justify-content: space-between
      align-items: flex-end
      width: 100%
      .info-content
        flex: 1
        min-width: 0
        overflow: hidden
        display: flex
        gap 8px
        .icon-item
          display: flex
          align-items: center
          gap: 2px
          .number
            color: var(--paragraph);
            font-size: var(--b2-font-size);
            font-weight: var(--number-regular-font-weight);
            line-height: var(--b2-line-height);
      .time
        flex-shrink: 0
        margin-left: 6px
        color: var(--paragraph)
        text-align: right
        font-family: "PingFang SC"
        font-size: var(--c2-font-size)
        font-weight: var(--c2-font-weight)
        line-height: var(--c2-line-height)
</style>
