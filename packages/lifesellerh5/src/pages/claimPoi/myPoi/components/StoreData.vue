<template>
  <div class="store-data">
    <!-- 门店数据卡片 -->
    <div class="data-card">
      <!-- 数据标题 -->
      <div class="section-title">
        <span>门店数据</span>
      </div>

      <!-- 统计指标行 -->
      <div class="metrics-row">
        <!-- 新增访问 -->
        <div
          v-for="item in shopDataMetrics"
          :key="item.key"
          class="metric-item"
          :class="{ active: activeShopChartData === item.key }"
          @click="activeShopChartData = item.key"
        >
          <div class="metric-label">{{ item.name }}</div>
          <div class="metric-value">{{ overviewData?.[item.key]?.value }}</div>
          <div class="metric-trend" :class="{
            // @ts-ignore
            up: overviewData?.[item.key]?.ratio > 0,
            // @ts-ignore
            down: overviewData?.[item.key]?.ratio < 0,
          }"
          >
            <span>{{ (overviewData?.[item.key]?.ratio ?? '-') === '-' ? '-' : `${overviewData?.[item.key]?.ratio}%` }}</span>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-container">
        <div ref="shopChartRef" class="chart"></div>
      </div>
    </div>

    <!-- 笔记数据卡片 -->
    <div class="data-card">
      <!-- 数据标题 -->
      <div class="section-title">
        <span>笔记数据</span>
      </div>

      <!-- 统计指标行 -->
      <div class="metrics-row">
        <!-- 笔记数据指标 -->
        <div
          v-for="item in notesDataMetrics"
          :key="item.key"
          class="metric-item"
          :class="{ active: activeNotesChartData === item.key }"
          @click="activeNotesChartData = item.key"
        >
          <div class="metric-label">{{ item.name }}</div>
          <div class="metric-value">{{ overviewData?.[item.key]?.value }}</div>
          <div class="metric-trend" :class="{
            // @ts-ignore
            up: overviewData?.[item.key]?.ratio > 0,
            // @ts-ignore
            down: overviewData?.[item.key]?.ratio < 0,
          }"
          >
            <span>{{ (overviewData?.[item.key]?.ratio ?? '-') === '-' ? '-' : `${overviewData?.[item.key]?.ratio}%` }}</span>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-container">
        <div ref="noteChartRef" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref, onMounted, onUnmounted, watch,
  } from 'vue'
  import * as echarts from 'echarts'
  import dayjs from 'dayjs'
  import { postCommonBusinessdata } from '@edith/edith_post_common_businessdata'
  import { showToast } from '@xhs/reds-h5-next'
  import { IDataValue, IMetrics } from '../type'
  import { lineChartCommonOption } from '../dataCenter/constant'

  const props = defineProps<{
    dateRange: string
    selectedStoreId: string
  }>()

  const loading = ref(false)
  const emit = defineEmits(['update:dateRange'])

  // 图表引用
  const shopChartRef = ref<HTMLElement | null>(null)
  const noteChartRef = ref<HTMLElement | null>(null)

  let shopChart
  let noteChart

  const activeShopChartData = ref('view_num')
  const shopDataMetrics = ref<IMetrics[]>([
    {
      key: 'view_num',
      name: '新增访问',
    },
    {
      key: 'sign_cnt',
      name: '签到',
    },
    {
      key: 'comment_cnt',
      name: '留言',
    },
    {
      key: 'declare_cnt',
      name: '表态',
    },
  ])
  const activeNotesChartData = ref('note_cnt')
  const notesDataMetrics = ref<IMetrics[]>([
    {
      key: 'note_cnt',
      name: '新增笔记数',
    },
    {
      key: 'note_read_num',
      name: '笔记浏览',
    },
    {
      key: 'note_interact_num',
      name: '笔记互动',
    },
  ])

  const chartData = ref<IDataValue[]>([])
  function getChartData(dateType: string, blockKey: string) {
    postCommonBusinessdata({
      requestBody: {
        blockElements: [
          {
            blockKey,
            filterMap: {
              twc: 'pre_1d',
              dateType,
              shopIdList: props.selectedStoreId,
            }
          }
        ],
      }
    }).then(res => {
      const data = JSON.parse(res.responseJson)
      // 转换数据格式，提取嵌套的value值
      const formattedData = data?.[0]?.data?.map((item: any) => {
        const result: any = {}
        // 遍历对象的每个属性
        Object.keys(item)?.forEach(key => {
          // 如果属性是对象且包含value字段，则直接取value值
          if (item[key] && typeof item[key] === 'object' && 'value' in item[key]) {
            if (key === 'time_period') {
              // 提取日期字符串，例如从 '20250607_20250607' 中提取第一个日期
              const dateStr = item[key].value?.split('_')?.[0]
              if (dateStr && dateStr.length >= 8) {
                // 提取月份和日期(后四位)，转换为 'MM-DD' 格式
                const month = dateStr.substring(4, 6)
                const day = dateStr.substring(6, 8)
                result[key] = `${month}-${day}`
              } else {
                result[key] = dateStr
              }
            } else {
              result[key] = item[key].value
            }
          } else {
            // 否则保留原值
            result[key] = item[key]
          }
        })
        return result
      }) || []
      // 按日期从小到大排序
      formattedData.sort((a, b) => {
        // 将MM-DD格式转换回可比较的数值
        const getNumericDate = dateStr => {
          if (!dateStr) return 0
          const [month, day] = dateStr.split('-').map(Number)
          return month * 100 + day
        }
        return getNumericDate(a?.time_period) - getNumericDate(b?.time_period)
      })
      chartData.value = formattedData

      const option = {
        ...lineChartCommonOption,
        xAxis: {
          ...lineChartCommonOption.xAxis,
          data: chartData.value.map(item => item?.time_period),
        },
      }
      shopChart.setOption({
        ...option,
        series: [
          {
            ...lineChartCommonOption.series[0],
            data: chartData.value?.map(item => item[activeShopChartData.value]) ?? [],
          },
        ],
      })
      // console.log('chartData.value', chartData.value?.map(item => item[activeShopChartData.value]) ?? [])
      noteChart.setOption({
        ...option,
        series: [
          {
            ...lineChartCommonOption.series[0],
            data: chartData.value?.map(item => item[activeNotesChartData.value]) ?? [],
          },
        ],
      })
    }).catch(err => {
      showToast.error({
        message: err?.message || '获取门店数据失败',
      })
    })
  }

  const overviewData = ref<IDataValue>({})
  function getOverviewData(twc: string = 'pre_7d', blockKey: string) {
    postCommonBusinessdata({
      requestBody: {
        blockElements: [
          {
            blockKey,
            filterMap: {
              twc,
              dateType: '1',
              date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
              shopIdList: props.selectedStoreId,
            }
          }
        ],
      }
    }).then(res => {
      const data = JSON.parse(res.responseJson)
      let finalData
      if (data?.[0]?.data?.length === 2) {
        const date1 = data?.[0]?.data?.[0]?.time_period?.value?.split('_')?.[0]
        const date2 = data?.[0]?.data?.[1]?.time_period?.value?.split('_')?.[0]
        finalData = date1 > date2 ? data?.[0]?.data?.[0] : data?.[0]?.data?.[1]
      } else {
        finalData = data?.[0]?.data?.[0]
      }
      overviewData.value = {
        ...finalData,
      }
    }).catch(err => {
      showToast.error({
        message: err?.message || '获取门店数据失败',
      })
    })
  }

  watch(() => props.dateRange, newVal => {
    let startDate
    let endDate
    if (newVal === '2') {
      // 最近7天，不包括今天
      startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
      endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    } else if (newVal === '3') {
      // 最近30天，不包括今天
      startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
      endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    } else {
      startDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    }
    emit('update:dateRange', {
      start: startDate,
      end: endDate,
    })
  }, { immediate: true })

  watch(() => [props.dateRange, props.selectedStoreId], async ([newDateRange, newSelectedStoreId]) => {
    loading.value = true
    try {
      const overviewBlockKey = newSelectedStoreId ? 'appUserShopDetailData' : 'appUserAllShopDetailData'
      const chartBlockKey = newSelectedStoreId ? 'appUserShopDetailDataTimeLine' : 'appUserAllShopDetailDataTimeLine'
      const timeWindow = newDateRange === '2' ? 'pre_7d' : 'pre_30d'
      await Promise.all([
        getChartData(newDateRange, chartBlockKey),
        getOverviewData(timeWindow, overviewBlockKey)
      ])
    } finally {
      loading.value = false
    }
  }, { immediate: true })

  watch(() => activeShopChartData.value, () => {
    shopChart.setOption({
      ...shopChart.getOption(),
      series: [
        {
          ...shopChart.getOption().series[0],
          data: chartData.value.map(item => item[activeShopChartData.value]),
        },
      ],
    })
  })

  watch(() => activeNotesChartData.value, () => {
    noteChart.setOption({
      ...noteChart.getOption(),
      series: [
        {
          ...noteChart.getOption().series[0],
          data: chartData.value.map(item => item[activeNotesChartData.value]),
        },
      ],
    })
  })

  // 保存图表实例用于销毁
  const chartInstances: echarts.ECharts[] = []

  // 初始化图表
  const initChart = () => {
    // 创建主图表
    shopChart = echarts.init(shopChartRef.value)
    noteChart = echarts.init(noteChartRef.value)
    chartInstances.push(shopChart)

    // 设置并渲染图表
    shopChart.setOption(lineChartCommonOption)
    noteChart.setOption(lineChartCommonOption)
    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', () => {
      shopChart.resize()
      noteChart.resize()
    })
  }

  onMounted(() => {
    initChart()
  })

  onUnmounted(() => {
    // 销毁图表实例，避免内存泄漏
    chartInstances.forEach(chart => {
      chart.dispose()
    })
  })
</script>

<style scoped lang="stylus">
.store-data
  width 100%

.data-card
  background #fff
  border-radius 12px
  margin 16px
  padding 16px

  & + .data-card
    margin-top 8px

.section-title
  display flex
  align-items center
  font-size 14px
  color var(--title)
  font-weight 500
  margin-bottom 12px

.metrics-row
  display flex
  width 100%
  gap 8px
  margin-bottom 12px

.metric-item
  flex 1
  display flex
  flex-direction column
  gap 4px
  border-radius 8px
  border 0.5px solid var(--separator)
  padding 8px
  position relative

  &.active
    border-color #3d8af5
    border-width 1px
    background-color rgba(61, 138, 245, 0.03)

.metric-label
  font-size 12px
  color var(--paragraph)
  line-height 14px

.metric-value
  font-size 16px
  font-weight 700
  color var(--title)
  font-family "RED Number"
  line-height 18px

.metric-trend
  font-size 12px
  color var(--paragraph)
  line-height 14px

  &.up
    color #ff5252

  &.down
    color #52c41a

.chart-container
  width 100%
  margin-top 12px

.chart
  width 100%
  height 178px
</style>
