<template>
  <div class="manual-input-page">
    <!-- 导航栏 -->
    <NavBar title="手动输入菜单" />

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 菜单基本信息 -->
      <div class="menu-info-card">
        <div class="card-header">
          <Text type="T2" weight="500" color="#333">
            菜单基本信息
          </Text>
        </div>
        <div class="form-item">
          <Text type="T3" color="#333" class="form-label">
            菜单名称
          </Text>
          <input
            v-model="menuForm.name"
            type="text"
            placeholder="请输入菜单名称"
            class="form-input"
          />
        </div>
      </div>

      <!-- 菜品分类 -->
      <div class="category-section">
        <div class="section-header">
          <Text type="T2" weight="500" color="#333">
            菜品分类
          </Text>
          <Button
            type="text"
            size="small"
            @click="handleAddCategory"
          >
            <Icon name="plus" />
            添加分类
          </Button>
        </div>

        <div class="category-list">
          <div
            v-for="(category, categoryIndex) in menuForm.categories"
            :key="category.id"
            class="category-item"
          >
            <!-- 分类标题 -->
            <div class="category-header">
              <input
                v-model="category.name"
                type="text"
                placeholder="请输入分类名称"
                class="category-name-input"
              />
              <Button
                type="text"
                size="small"
                @click="handleDeleteCategory(categoryIndex)"
              >
                <Icon name="delete" color="#ff4757" />
              </Button>
            </div>

            <!-- 菜品列表 -->
            <div class="dish-list">
              <div
                v-for="(dish, dishIndex) in category.items"
                :key="dish.id"
                class="dish-item"
              >
                <div class="dish-form">
                  <div class="form-row">
                    <div class="form-item">
                      <Text type="T4" color="#666" class="form-label">
                        菜品名称
                      </Text>
                      <input
                        v-model="dish.name"
                        type="text"
                        placeholder="请输入菜品名称"
                        class="form-input"
                      />
                    </div>
                    <div class="form-item">
                      <Text type="T4" color="#666" class="form-label">
                        价格
                      </Text>
                      <input
                        v-model="dish.price"
                        type="number"
                        placeholder="0"
                        class="form-input price-input"
                      />
                    </div>
                  </div>
                  <div class="form-item">
                    <Text type="T4" color="#666" class="form-label">
                      描述（可选）
                    </Text>
                    <textarea
                      v-model="dish.description"
                      placeholder="请输入菜品描述"
                      class="form-textarea"
                      rows="2"
                    />
                  </div>
                </div>
                <Button
                  type="text"
                  size="small"
                  class="delete-dish-btn"
                  @click="handleDeleteDish(categoryIndex, dishIndex)"
                >
                  <Icon name="delete" color="#ff4757" />
                </Button>
              </div>

              <!-- 添加菜品按钮 -->
              <Button
                type="dashed"
                class="add-dish-btn"
                @click="handleAddDish(categoryIndex)"
              >
                <Icon name="plus" />
                添加菜品
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <Empty
        v-if="menuForm.categories.length === 0"
        class="empty-state"
        description="暂无菜品分类"
      >
        <template #image>
          <div class="empty-icon">🍽️</div>
        </template>
        <template #footer>
          <Button
            type="primary"
            @click="handleAddCategory"
          >
            添加第一个分类
          </Button>
        </template>
      </Empty>

      <!-- 底部操作 -->
      <div class="bottom-actions" v-if="menuForm.categories.length > 0">
        <Button
          type="default"
          class="action-btn"
          @click="handlePreview"
        >
          预览菜单
        </Button>
        <Button
          type="primary"
          class="action-btn"
          :disabled="!canSave"
          @click="handleSave"
        >
          保存菜单
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Text, Button, Icon, Empty, Toast } from '@xhs/reds-h5-next'
import NavBar from '../../components-next/NavBar.vue'
import type { MenuData, MenuCategory, MenuItem } from '../../types/menu'

// 路由
const router = useRouter()

// 响应式数据
const menuForm = ref<Partial<MenuData>>({
  name: '',
  categories: []
})

// 计算属性
const canSave = computed(() => {
  const categories = menuForm.value.categories
  return menuForm.value.name?.trim() &&
         categories && categories.length > 0 &&
         categories.some(cat =>
           cat.name?.trim() && cat.items && cat.items.length > 0 &&
           cat.items.some(item => item.name?.trim())
         )
})

// 方法
const generateId = () => Date.now().toString() + Math.random().toString(36).substring(2, 11)

const handleAddCategory = () => {
  const newCategory: MenuCategory = {
    id: generateId(),
    name: '',
    items: [],
    sort: menuForm.value.categories?.length || 0
  }

  if (!menuForm.value.categories) {
    menuForm.value.categories = []
  }

  menuForm.value.categories.push(newCategory)
}

const handleDeleteCategory = (index: number) => {
  if (menuForm.value.categories) {
    menuForm.value.categories.splice(index, 1)
  }
}

const handleAddDish = (categoryIndex: number) => {
  const newDish: MenuItem = {
    id: generateId(),
    name: '',
    description: '',
    price: 0,
    createTime: Date.now(),
    updateTime: Date.now()
  }

  if (menuForm.value.categories?.[categoryIndex]) {
    menuForm.value.categories[categoryIndex].items.push(newDish)
  }
}

const handleDeleteDish = (categoryIndex: number, dishIndex: number) => {
  if (menuForm.value.categories?.[categoryIndex]) {
    menuForm.value.categories[categoryIndex].items.splice(dishIndex, 1)
  }
}

const handlePreview = () => {
  if (!canSave.value) {
    Toast({
      type: 'fail',
      message: '请先完善菜单信息'
    })
    return
  }

  Toast({
    type: 'text',
    message: '预览菜单功能开发中'
  })
}

const handleSave = () => {
  if (!canSave.value) {
    Toast({
      type: 'fail',
      message: '请先完善菜单信息'
    })
    return
  }

  Toast({
    type: 'success',
    message: '菜单保存成功'
  })

  // 跳转到菜单列表页面
  router.push('/lifesellerh5/menu/list')
}
</script>

<style scoped lang="stylus">
.manual-input-page
  min-height 100vh
  background-color #f5f5f5
  padding-bottom 80px

  .page-content
    padding 16px

    .menu-info-card
      background white
      border-radius 8px
      padding 16px
      margin-bottom 16px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)

      .card-header
        margin-bottom 16px

      .form-item
        .form-label
          display block
          margin-bottom 8px

        .form-input
          width 100%
          height 44px
          padding 0 12px
          border 1px solid #e5e5e5
          border-radius 6px
          font-size 16px
          background white

          &:focus
            border-color #ff2442
            outline none

          &::placeholder
            color #ccc

    .category-section
      .section-header
        display flex
        justify-content space-between
        align-items center
        margin-bottom 16px

      .category-list
        .category-item
          background white
          border-radius 8px
          padding 16px
          margin-bottom 16px
          box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)

          .category-header
            display flex
            align-items center
            gap 12px
            margin-bottom 16px

            .category-name-input
              flex 1
              height 40px
              padding 0 12px
              border 1px solid #e5e5e5
              border-radius 6px
              font-size 16px
              font-weight 500
              background white

              &:focus
                border-color #ff2442
                outline none

              &::placeholder
                color #ccc

          .dish-list
            .dish-item
              position relative
              border 1px solid #f0f0f0
              border-radius 6px
              padding 12px
              margin-bottom 12px
              background #fafafa

              .dish-form
                .form-row
                  display flex
                  gap 12px
                  margin-bottom 12px

                  .form-item
                    flex 1

                    &:last-child
                      flex 0 0 100px

                .form-item
                  .form-label
                    display block
                    margin-bottom 6px
                    font-size 12px

                  .form-input
                    width 100%
                    height 36px
                    padding 0 8px
                    border 1px solid #e5e5e5
                    border-radius 4px
                    font-size 14px
                    background white

                    &.price-input
                      text-align right

                    &:focus
                      border-color #ff2442
                      outline none

                    &::placeholder
                      color #ccc

                  .form-textarea
                    width 100%
                    padding 8px
                    border 1px solid #e5e5e5
                    border-radius 4px
                    font-size 14px
                    background white
                    resize vertical
                    min-height 60px

                    &:focus
                      border-color #ff2442
                      outline none

                    &::placeholder
                      color #ccc

              .delete-dish-btn
                position absolute
                top 8px
                right 8px
                background rgba(255, 255, 255, 0.8)
                border-radius 4px
                padding 4px

            .add-dish-btn
              width 100%
              height 40px
              border 1px dashed #d9d9d9
              border-radius 6px
              background transparent
              color #666
              display flex
              align-items center
              justify-content center
              gap 6px

              &:hover
                border-color #ff2442
                color #ff2442

    .empty-state
      margin-top 60px

      .empty-icon
        font-size 48px
        margin-bottom 16px

  .bottom-actions
    position fixed
    bottom 0
    left 0
    right 0
    background white
    padding 16px
    border-top 1px solid #f0f0f0
    display flex
    gap 12px
    z-index 10

    .action-btn
      flex 1
      height 48px
      border-radius 8px
      font-size 16px
      font-weight 500
</style>