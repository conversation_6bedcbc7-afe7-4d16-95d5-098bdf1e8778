<template>
  <div class="photo-generate-page">
    <!-- 导航栏 -->
    <NavBar title="拍照生成菜单" />

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 使用说明 -->
      <div class="instruction-card">
        <div class="instruction-header">
          <Icon name="info-circle" color="#ff2442" />
          <Text type="T2" weight="500" color="#333">
            使用说明
          </Text>
        </div>
        <div class="instruction-content">
          <Text type="T3" color="#666" line-height="20px">
            请拍摄清晰的菜单照片，系统将自动识别菜品名称和价格。为了获得更好的识别效果，请确保：
          </Text>
          <ul class="instruction-list">
            <li>光线充足，菜单内容清晰可见</li>
            <li>避免反光和阴影</li>
            <li>菜单文字尽量水平拍摄</li>
            <li>一次拍摄一页菜单</li>
          </ul>
        </div>
      </div>

      <!-- 拍照区域 -->
      <div class="photo-section">
        <div class="photo-header">
          <Text type="T2" weight="500" color="#333">
            拍摄菜单
          </Text>
          <Text type="T3" color="#666">
            已拍摄 {{ photoList.length }} 张
          </Text>
        </div>

        <!-- 照片列表 -->
        <div class="photo-list" v-if="photoList.length > 0">
          <div
            v-for="(photo, index) in photoList"
            :key="photo.id"
            class="photo-item"
          >
            <img :src="photo.url" :alt="`菜单照片${index + 1}`" class="photo-image" />
            <div class="photo-overlay">
              <div class="photo-status" :class="photo.status">
                <Icon
                  :name="getStatusIcon(photo.status)"
                  :color="getStatusColor(photo.status)"
                />
                <Text type="T4" :color="getStatusColor(photo.status)">
                  {{ getStatusText(photo.status) }}
                </Text>
              </div>
              <Button
                type="text"
                size="small"
                class="delete-btn"
                @click="handleDeletePhoto(index)"
              >
                <Icon name="delete" color="#ff4757" />
              </Button>
            </div>
          </div>
        </div>

        <!-- 拍照按钮 -->
        <div class="photo-actions">
          <Button
            type="default"
            class="photo-btn"
            :loading="uploading"
            @click="handleTakePhoto"
          >
            <Icon name="camera" />
            {{ photoList.length > 0 ? '继续拍摄' : '开始拍摄' }}
          </Button>
        </div>
      </div>

      <!-- 识别结果 -->
      <div class="result-section" v-if="recognitionResults.length > 0">
        <div class="result-header">
          <Text type="T2" weight="500" color="#333">
            识别结果
          </Text>
          <Text type="T3" color="#666">
            共识别到 {{ recognitionResults.length }} 道菜品
          </Text>
        </div>

        <div class="result-list">
          <div
            v-for="(item, index) in recognitionResults"
            :key="index"
            class="result-item"
          >
            <div class="item-info">
              <Text type="T2" weight="500" color="#333">
                {{ item.name }}
              </Text>
              <Text type="T3" color="#ff2442" v-if="item.price">
                ¥{{ item.price }}
              </Text>
            </div>
            <Button
              type="text"
              size="small"
              @click="handleEditItem(item, index)"
            >
              编辑
            </Button>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="bottom-actions" v-if="photoList.length > 0">
        <Button
          type="default"
          class="action-btn"
          @click="handlePreview"
        >
          预览菜单
        </Button>
        <Button
          type="primary"
          class="action-btn"
          :disabled="recognitionResults.length === 0"
          @click="handleSave"
        >
          保存菜单
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Text, Button, Icon, Toast } from '@xhs/reds-h5-next'
import NavBar from '../../components-next/NavBar.vue'

// 路由
const router = useRouter()

// 响应式数据
const uploading = ref(false)
const photoList = ref<Array<{
  id: string
  url: string
  status: 'uploading' | 'success' | 'failed' | 'recognizing'
}>>([])

const recognitionResults = ref<Array<{
  name: string
  price?: number
  category?: string
}>>([])

// 方法
const handleTakePhoto = async () => {
  try {
    uploading.value = true

    // Mock 拍照和上传过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newPhoto = {
      id: Date.now().toString(),
      url: `https://picasso-static.xiaohongshu.com/fe-platform/menu-photo-${photoList.value.length + 1}.jpg`,
      status: 'recognizing' as const
    }

    photoList.value.push(newPhoto)

    // Mock 识别过程
    setTimeout(() => {
      const photoIndex = photoList.value.findIndex(p => p.id === newPhoto.id)
      if (photoIndex !== -1) {
        photoList.value[photoIndex].status = 'success'

        // Mock 识别结果
        const mockResults = [
          { name: '宫保鸡丁', price: 28, category: '热菜' },
          { name: '麻婆豆腐', price: 18, category: '热菜' },
          { name: '酸辣土豆丝', price: 15, category: '素菜' }
        ]

        recognitionResults.value.push(...mockResults)
      }
    }, 2000)

    Toast({
      type: 'success',
      message: '照片上传成功，正在识别中...'
    })

  } catch (error) {
    console.error('拍照失败:', error)
    Toast({
      type: 'fail',
      message: '拍照失败，请重试'
    })
  } finally {
    uploading.value = false
  }
}

const handleDeletePhoto = (index: number) => {
  photoList.value.splice(index, 1)
  // 这里可以添加删除对应识别结果的逻辑
}

const getStatusIcon = (status: string) => {
  const iconMap = {
    uploading: 'loading',
    success: 'check-circle',
    failed: 'close-circle',
    recognizing: 'loading'
  }
  return iconMap[status] || 'loading'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    uploading: '#1890ff',
    success: '#52c41a',
    failed: '#ff4d4f',
    recognizing: '#fa8c16'
  }
  return colorMap[status] || '#666'
}

const getStatusText = (status: string) => {
  const textMap = {
    uploading: '上传中',
    success: '识别完成',
    failed: '识别失败',
    recognizing: '识别中'
  }
  return textMap[status] || '未知'
}

const handleEditItem = (item: any, _index: number) => {
  Toast({
    type: 'text',
    message: `编辑菜品: ${item.name}`
  })
}

const handlePreview = () => {
  Toast({
    type: 'text',
    message: '预览菜单功能开发中'
  })
}

const handleSave = () => {
  Toast({
    type: 'success',
    message: '菜单保存成功'
  })
  // 跳转到菜单列表页面
  router.push('/lifesellerh5/menu/list')
}
</script>

<style scoped lang="stylus">
.photo-generate-page
  min-height 100vh
  background-color #f5f5f5

  .page-content
    padding 16px

    .instruction-card
      background white
      border-radius 8px
      padding 16px
      margin-bottom 16px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)

      .instruction-header
        display flex
        align-items center
        gap 8px
        margin-bottom 12px

      .instruction-content
        .instruction-list
          margin 12px 0 0 0
          padding-left 16px

          li
            margin-bottom 8px
            color #666
            font-size 14px
            line-height 1.4

            &:last-child
              margin-bottom 0

    .photo-section
      background white
      border-radius 8px
      padding 16px
      margin-bottom 16px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)

      .photo-header
        display flex
        justify-content space-between
        align-items center
        margin-bottom 16px

      .photo-list
        display grid
        grid-template-columns repeat(2, 1fr)
        gap 12px
        margin-bottom 16px

        .photo-item
          position relative
          aspect-ratio 4/3
          border-radius 8px
          overflow hidden

          .photo-image
            width 100%
            height 100%
            object-fit cover

          .photo-overlay
            position absolute
            top 0
            left 0
            right 0
            bottom 0
            background rgba(0, 0, 0, 0.3)
            display flex
            flex-direction column
            justify-content space-between
            padding 8px

            .photo-status
              display flex
              align-items center
              gap 4px

            .delete-btn
              align-self flex-end
              background rgba(255, 255, 255, 0.2)
              border-radius 4px
              padding 4px

      .photo-actions
        text-align center

        .photo-btn
          width 100%
          height 48px
          border-radius 8px
          display flex
          align-items center
          justify-content center
          gap 8px

    .result-section
      background white
      border-radius 8px
      padding 16px
      margin-bottom 16px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)

      .result-header
        display flex
        justify-content space-between
        align-items center
        margin-bottom 16px

      .result-list
        .result-item
          display flex
          justify-content space-between
          align-items center
          padding 12px 0
          border-bottom 1px solid #f0f0f0

          &:last-child
            border-bottom none

          .item-info
            flex 1

            > *
              display block
              margin-bottom 4px

              &:last-child
                margin-bottom 0

    .bottom-actions
      position fixed
      bottom 0
      left 0
      right 0
      background white
      padding 16px
      border-top 1px solid #f0f0f0
      display flex
      gap 12px

      .action-btn
        flex 1
        height 48px
        border-radius 8px
        font-size 16px
        font-weight 500
</style>