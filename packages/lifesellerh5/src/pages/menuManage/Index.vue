<template>
  <div class="menu-manage-page">
    <!-- 导航栏 -->
    <NavBar title="菜单管理" />

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 菜单状态卡片 -->
      <div class="status-card">
        <div class="status-info">
          <Text type="T1" weight="500" color="#333">
            当前菜单状态
          </Text>
          <Text type="T3" color="#666" class="status-desc">
            {{ statusText }}
          </Text>
        </div>
        <div class="status-badge" :class="statusClass">
          {{ statusText }}
        </div>
      </div>

      <!-- 菜单列表 -->
      <div class="menu-list" v-if="menuList.length > 0">
        <div class="list-header">
          <Text type="T2" weight="500" color="#333">
            菜单列表
          </Text>
          <Button
            type="primary"
            size="small"
            @click="handleAddMenu"
          >
            添加菜单
          </Button>
        </div>

        <div class="menu-items">
          <div
            v-for="menu in menuList"
            :key="menu.id"
            class="menu-item"
            @click="handleMenuClick(menu)"
          >
            <div class="menu-info">
              <Text type="T2" weight="500" color="#333">
                {{ menu.name }}
              </Text>
              <Text type="T3" color="#666">
                {{ menu.categories.length }}个分类 · {{ getTotalItems(menu) }}道菜品
              </Text>
              <Text type="T4" color="#999">
                更新时间：{{ formatTime(menu.updateTime) }}
              </Text>
            </div>
            <Icon name="arrow-right" color="#ccc" />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <Empty
        v-else
        class="empty-state"
        description="暂无菜单数据"
      >
        <template #image>
          <div class="empty-icon">📋</div>
        </template>
        <template #footer>
          <Button
            type="primary"
            @click="handleAddMenu"
          >
            创建第一个菜单
          </Button>
        </template>
      </Empty>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Text, Button, Icon, Empty, Toast } from '@xhs/reds-h5-next'
import NavBar from '../../components-next/NavBar.vue'
import { MenuStatusChecker, MENU_CONFIG } from '../../services/menu'
import type { MenuData, MenuStatus } from '../../types/menu'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const menuList = ref<MenuData[]>([])
const currentStatus = ref<MenuStatus>()

// 计算属性
const statusText = computed(() => {
  return currentStatus.value ? MenuStatusChecker.getStatusText(currentStatus.value) : '未知'
})

const statusClass = computed(() => {
  const status = currentStatus.value
  if (!status) return ''

  const classMap = {
    NOT_CREATED: 'status-not-created',
    DRAFT: 'status-draft',
    SUBMITTED: 'status-submitted',
    REVIEWING: 'status-reviewing',
    APPROVED: 'status-approved',
    REJECTED: 'status-rejected'
  }

  return classMap[status] || ''
})

// 方法
const loadMenuList = async () => {
  try {
    loading.value = true
    // 这里应该调用真实的API获取菜单列表
    // const response = await getMenuList()
    // menuList.value = response.list

    // Mock 数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    menuList.value = []
    currentStatus.value = 'NOT_CREATED' as MenuStatus
  } catch (error) {
    console.error('获取菜单列表失败:', error)
    Toast({
      type: 'fail',
      message: '获取菜单列表失败，请稍后重试'
    })
  } finally {
    loading.value = false
  }
}

const getTotalItems = (menu: MenuData): number => {
  return menu.categories.reduce((total, category) => total + category.items.length, 0)
}

const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleAddMenu = () => {
  // 跳转到菜单创建页面
  router.push(MENU_CONFIG.ROUTES.PHOTO_GENERATE)
}

const handleMenuClick = (menu: MenuData) => {
  // 跳转到菜单详情/编辑页面
  console.log('点击菜单:', menu)
  Toast({
    type: 'text',
    message: `点击了菜单: ${menu.name}`
  })
}

// 生命周期
onMounted(() => {
  loadMenuList()
})
</script>

<style scoped lang="stylus">
.menu-manage-page
  min-height 100vh
  background-color #f5f5f5
  position relative

  .page-content
    padding 16px

    .status-card
      background white
      border-radius 8px
      padding 16px
      margin-bottom 16px
      display flex
      justify-content space-between
      align-items center
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)

      .status-info
        .status-desc
          margin-top 4px

      .status-badge
        padding 4px 8px
        border-radius 4px
        font-size 12px
        font-weight 500

        &.status-not-created
          background-color #f0f0f0
          color #666

        &.status-draft
          background-color #fff7e6
          color #fa8c16

        &.status-submitted
          background-color #e6f7ff
          color #1890ff

        &.status-reviewing
          background-color #fff1f0
          color #ff4d4f

        &.status-approved
          background-color #f6ffed
          color #52c41a

        &.status-rejected
          background-color #fff2f0
          color #ff4d4f

    .menu-list
      .list-header
        display flex
        justify-content space-between
        align-items center
        margin-bottom 12px

      .menu-items
        .menu-item
          background white
          border-radius 8px
          padding 16px
          margin-bottom 8px
          display flex
          justify-content space-between
          align-items center
          box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)
          cursor pointer
          transition all 0.2s ease

          &:active
            background-color #f5f5f5

          .menu-info
            flex 1

            > *
              display block
              margin-bottom 4px

              &:last-child
                margin-bottom 0

    .empty-state
      margin-top 60px

      .empty-icon
        font-size 48px
        margin-bottom 16px

  .loading-overlay
    position absolute
    top 0
    left 0
    right 0
    bottom 0
    background-color rgba(255, 255, 255, 0.8)
    display flex
    align-items center
    justify-content center
    z-index 10

    .loading-spinner
      width 24px
      height 24px
      border 2px solid #f3f3f3
      border-top 2px solid #ff2442
      border-radius 50%
      animation spin 1s linear infinite

@keyframes spin
  0%
    transform rotate(0deg)
  100%
    transform rotate(360deg)
</style>