<template>
  <div class="menu-demo-page">
    <!-- 导航栏 -->
    <NavBar title="菜单管理演示" />
    
    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 使用说明 -->
      <div class="instruction-card">
        <Text type="T1" weight="500" color="#333" class="title">
          菜单管理功能演示
        </Text>
        <Text type="T3" color="#666" line-height="20px" class="description">
          这是一个完整的菜单管理功能演示页面，包含了菜单管理按钮、引导面板、以及根据商家菜单状态的不同交互逻辑。
        </Text>
      </div>
      
      <!-- 功能演示区域 -->
      <div class="demo-section">
        <div class="demo-header">
          <Text type="T2" weight="500" color="#333">
            功能演示
          </Text>
        </div>
        
        <!-- 菜单管理组件 -->
        <div class="demo-item">
          <Text type="T3" color="#666" class="demo-label">
            菜单管理按钮（根据商家状态显示不同内容）
          </Text>
          <MenuManage
            button-text="菜单管理"
            button-type="primary"
            @photo-generate="handlePhotoGenerate"
            @manual-input="handleManualInput"
            @navigate-to-list="handleNavigateToList"
          />
        </div>
        
        <!-- 状态切换 -->
        <div class="demo-item">
          <Text type="T3" color="#666" class="demo-label">
            模拟不同商家状态（开发测试用）
          </Text>
          <div class="status-buttons">
            <Button
              type="default"
              size="small"
              @click="simulateNewMerchant"
            >
              模拟新商家
            </Button>
            <Button
              type="default"
              size="small"
              @click="simulateExistingMerchant"
            >
              模拟已有菜单商家
            </Button>
          </div>
        </div>
      </div>
      
      <!-- 功能特性 -->
      <div class="features-section">
        <div class="features-header">
          <Text type="T2" weight="500" color="#333">
            功能特性
          </Text>
        </div>
        
        <div class="features-list">
          <div class="feature-item">
            <Icon name="check-circle" color="#52c41a" />
            <div class="feature-content">
              <Text type="T3" weight="500" color="#333">
                智能状态判断
              </Text>
              <Text type="T4" color="#666">
                根据商家菜单上传历史自动判断显示引导面板或跳转列表页面
              </Text>
            </div>
          </div>
          
          <div class="feature-item">
            <Icon name="camera" color="#1890ff" />
            <div class="feature-content">
              <Text type="T3" weight="500" color="#333">
                拍照自动生成
              </Text>
              <Text type="T4" color="#666">
                支持拍摄实体菜单，AI自动识别菜品名称和价格
              </Text>
            </div>
          </div>
          
          <div class="feature-item">
            <Icon name="edit" color="#fa8c16" />
            <div class="feature-content">
              <Text type="T3" weight="500" color="#333">
                手动输入菜单
              </Text>
              <Text type="T4" color="#666">
                支持手动创建菜单分类和菜品，灵活管理菜单内容
              </Text>
            </div>
          </div>
          
          <div class="feature-item">
            <Icon name="mobile" color="#722ed1" />
            <div class="feature-content">
              <Text type="T3" weight="500" color="#333">
                移动端优化
              </Text>
              <Text type="T4" color="#666">
                完全适配移动端，支持触摸操作和响应式布局
              </Text>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 快捷操作 -->
      <div class="quick-actions">
        <Text type="T2" weight="500" color="#333" class="actions-title">
          快捷操作
        </Text>
        <div class="actions-grid">
          <div class="action-card" @click="goToMenuList">
            <Icon name="list" color="#ff2442" />
            <Text type="T3" color="#333">菜单列表</Text>
          </div>
          <div class="action-card" @click="goToPhotoGenerate">
            <Icon name="camera" color="#ff2442" />
            <Text type="T3" color="#333">拍照生成</Text>
          </div>
          <div class="action-card" @click="goToManualInput">
            <Icon name="edit" color="#ff2442" />
            <Text type="T3" color="#333">手动输入</Text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Text, Button, Icon, Toast } from '@xhs/reds-h5-next'
import { useRouter } from 'vue-router'
import NavBar from '../../components-next/NavBar.vue'
import MenuManage from '../../components/MenuManage/index.vue'
import { MENU_CONFIG } from '../../services/menu'

// 路由
const router = useRouter()

// 方法
const handlePhotoGenerate = () => {
  Toast({
    type: 'success',
    message: '跳转到拍照生成菜单页面'
  })
}

const handleManualInput = () => {
  Toast({
    type: 'success',
    message: '跳转到手动输入菜单页面'
  })
}

const handleNavigateToList = () => {
  Toast({
    type: 'success',
    message: '跳转到菜单管理列表页面'
  })
}

const simulateNewMerchant = () => {
  Toast({
    type: 'text',
    message: '已切换为新商家状态，点击菜单管理按钮将显示引导面板'
  })
}

const simulateExistingMerchant = () => {
  Toast({
    type: 'text',
    message: '已切换为已有菜单商家状态，点击菜单管理按钮将跳转到列表页面'
  })
}

const goToMenuList = () => {
  router.push(MENU_CONFIG.ROUTES.MENU_LIST)
}

const goToPhotoGenerate = () => {
  router.push(MENU_CONFIG.ROUTES.PHOTO_GENERATE)
}

const goToManualInput = () => {
  router.push(MENU_CONFIG.ROUTES.MANUAL_INPUT)
}
</script>

<style scoped lang="stylus">
.menu-demo-page
  min-height 100vh
  background-color #f5f5f5
  
  .page-content
    padding 16px
    
    .instruction-card
      background white
      border-radius 8px
      padding 20px
      margin-bottom 20px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)
      
      .title
        margin-bottom 12px
        
      .description
        line-height 1.5
    
    .demo-section
      background white
      border-radius 8px
      padding 16px
      margin-bottom 20px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)
      
      .demo-header
        margin-bottom 16px
        
      .demo-item
        margin-bottom 20px
        
        &:last-child
          margin-bottom 0
          
        .demo-label
          display block
          margin-bottom 12px
          
        .status-buttons
          display flex
          gap 8px
    
    .features-section
      background white
      border-radius 8px
      padding 16px
      margin-bottom 20px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)
      
      .features-header
        margin-bottom 16px
        
      .features-list
        .feature-item
          display flex
          align-items flex-start
          gap 12px
          margin-bottom 16px
          
          &:last-child
            margin-bottom 0
            
          .feature-content
            flex 1
            
            > *
              display block
              margin-bottom 4px
              
              &:last-child
                margin-bottom 0
    
    .quick-actions
      background white
      border-radius 8px
      padding 16px
      box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)
      
      .actions-title
        margin-bottom 16px
        
      .actions-grid
        display grid
        grid-template-columns repeat(3, 1fr)
        gap 12px
        
        .action-card
          display flex
          flex-direction column
          align-items center
          gap 8px
          padding 16px
          border 1px solid #f0f0f0
          border-radius 6px
          cursor pointer
          transition all 0.2s ease
          
          &:active
            background-color #f5f5f5
            transform scale(0.98)
            
  // 响应式适配
  @media (max-width: 375px)
    .page-content
      padding 12px
      
      .quick-actions
        .actions-grid
          grid-template-columns repeat(2, 1fr)
</style>
