/**
 * 菜单管理相关类型定义
 */

// 菜单项接口
export interface MenuItem {
  /** 菜单项ID */
  id: string
  /** 菜单项名称 */
  name: string
  /** 菜单项描述 */
  description?: string
  /** 菜单项价格 */
  price?: number
  /** 菜单项图片URL */
  imageUrl?: string
  /** 菜单项分类 */
  category?: string
  /** 创建时间 */
  createTime?: number
  /** 更新时间 */
  updateTime?: number
}

// 菜单分类接口
export interface MenuCategory {
  /** 分类ID */
  id: string
  /** 分类名称 */
  name: string
  /** 分类下的菜单项 */
  items: MenuItem[]
  /** 排序权重 */
  sort?: number
}

// 菜单数据接口
export interface MenuData {
  /** 菜单ID */
  id: string
  /** 菜单名称 */
  name: string
  /** 菜单分类列表 */
  categories: MenuCategory[]
  /** 菜单状态 */
  status: MenuStatus
  /** 创建时间 */
  createTime: number
  /** 更新时间 */
  updateTime: number
  /** 是否已提交 */
  isSubmitted: boolean
}

// 菜单状态枚举
export enum MenuStatus {
  /** 未创建 */
  NOT_CREATED = 'NOT_CREATED',
  /** 草稿状态 */
  DRAFT = 'DRAFT',
  /** 已提交 */
  SUBMITTED = 'SUBMITTED',
  /** 审核中 */
  REVIEWING = 'REVIEWING',
  /** 审核通过 */
  APPROVED = 'APPROVED',
  /** 审核拒绝 */
  REJECTED = 'REJECTED'
}

// 菜单上传状态接口
export interface MenuUploadStatus {
  /** 是否曾经上传过菜单（包括历史记录） */
  hasEverUploaded: boolean
  /** 是否有已提交的菜单 */
  hasSubmittedMenu: boolean
  /** 当前菜单状态 */
  currentStatus: MenuStatus
  /** 最新菜单数据 */
  latestMenu?: MenuData
}

// API 接口类型定义

// 获取菜单上传状态的请求参数
export interface GetMenuUploadStatusParams {
  /** 商家ID */
  sellerId?: string
}

// 获取菜单上传状态的响应数据
export interface GetMenuUploadStatusResponse {
  /** 返回码 */
  code: number
  /** 是否成功 */
  success: boolean
  /** 消息 */
  msg: string
  /** 数据 */
  data: MenuUploadStatus
}

// 获取菜单列表的请求参数
export interface GetMenuListParams {
  /** 商家ID */
  sellerId?: string
  /** 页码 */
  pageNo?: number
  /** 页面大小 */
  pageSize?: number
}

// 获取菜单列表的响应数据
export interface GetMenuListResponse {
  /** 返回码 */
  code: number
  /** 是否成功 */
  success: boolean
  /** 消息 */
  msg: string
  /** 数据 */
  data: {
    /** 总数 */
    total: number
    /** 菜单列表 */
    list: MenuData[]
  }
}

// 创建菜单的请求参数
export interface CreateMenuParams {
  /** 菜单名称 */
  name: string
  /** 菜单分类列表 */
  categories: MenuCategory[]
}

// 创建菜单的响应数据
export interface CreateMenuResponse {
  /** 返回码 */
  code: number
  /** 是否成功 */
  success: boolean
  /** 消息 */
  msg: string
  /** 数据 */
  data: MenuData
}

// 菜单管理组件的 Props 接口
export interface MenuManageProps {
  /** 是否显示菜单管理按钮 */
  visible?: boolean
  /** 按钮文本 */
  buttonText?: string
  /** 按钮类型 */
  buttonType?: 'primary' | 'default' | 'danger'
  /** 自定义样式类名 */
  className?: string
}

// 菜单管理组件的事件接口
export interface MenuManageEvents {
  /** 点击拍照生成菜单 */
  onPhotoGenerate: () => void
  /** 点击手动输入菜单 */
  onManualInput: () => void
  /** 跳转到菜单管理列表 */
  onNavigateToList: () => void
}

// 引导面板配置接口
export interface GuideSheetConfig {
  /** 标题 */
  title: string
  /** 提示文案 */
  description: string
  /** 示例图片URL */
  exampleImageUrl: string
  /** 操作按钮配置 */
  actions: {
    /** 拍照生成按钮文本 */
    photoText: string
    /** 手动输入按钮文本 */
    manualText: string
  }
}
