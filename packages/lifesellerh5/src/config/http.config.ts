/* eslint no-template-curly-in-string: 0 */

import { HttpConfig } from '@xhs/launcher'
import { generateLifeHost, generateEdithHost } from '~/utils/host'

const developmentHost = 'http://logan.devops.xiaohongshu.com/proxy/life-mobilemerchant-sj-beta'

// TODO 域名相关跟随商家版一起调整

export const LIFE_OLD_HOST = generateLifeHost(developmentHost)

const edithHost = generateEdithHost()

const httpConfig: HttpConfig = {
  BASE_URL: {
    // development: getLoganUrlByQuery(),
    // development: 'http://logan.devops.xiaohongshu.com/proxy/life-poiclaim-sit',
    development: developmentHost,
    // development: 'http://logan.devops.xiaohongshu.com/proxy/life-jczq-sit',
    // development: 'http://logan.devops.xiaohongshu.com/proxy/life-sit-qly',
    // development: 'http://life-dig.sl.sit.xiaohongshu.com',
    // formula build -e test
    test: edithHost,
    // formula build -e prerelease
    prerelease: edithHost,
    // formula build
    production: 'https://edith.xiaohongshu.com',
  },
  API_LIST: {
    GET_REDIRECT_ID: `${LIFE_OLD_HOST}/api/edith/general/mapping`,
    GET_POI_FEEDBACK_INFO: `${LIFE_OLD_HOST}/api/redlife/local/poi/info`,
    SUBMIT_POI_FEEDBACK: `${LIFE_OLD_HOST}/api/redlife/poi/user/feedback/submit`,
    GET_UPLOAD_TOKEN: `${LIFE_OLD_HOST}/api/redlife/merchant/upload/permit`,
    GET_UPLOADER_TOKEN: `${LIFE_OLD_HOST}/api/life/api/edith/upload/web/permit`, // 上传 ✅
    // ==========门店==========
    PROF_UPLOAD_TOKEN: `${LIFE_OLD_HOST}/api/edith/app/userqms/prof/upload/token`, // 新版获取图片上传token
    PROF_FILE_REPLACE: `${LIFE_OLD_HOST}/api/edith/app/userqms/prof/file/replace`, // 获取图片永久地址
    REDLIFE_UPLOAD_WEB_PERMIT: '/app/api/redlife/upload/web/permit', // 获取上传token
    POI_SUBMIT_BRAND_POI_REVIEWS: '/api/edith/app/poi/submit_brand_poi_reviews', // 提交门店装修
    POI_GET_BIZCOMPANY_INFO: `${LIFE_OLD_HOST}/api/edith/app/businesscenter/poi/getbizcompanyinfo`, // 获取当前用户营业执照
    POI_APPLY_DETAIL: `${LIFE_OLD_HOST}/api/edith/app/businesscenter/poi/applydetail`, // 认领门店详情

    // ==========门店管理==========
    POI_CLAIM_INFO_PAGE: '/app/api/redlife/life_service/poi/poi_claim_info/page', // 门店管理列表查询
    POI_UNBIND_POI: '/app/api/redlife/poi/unbind_poi', // 门店管理 -> 解绑
    REDLIFE_COMMON_APPLY_RECORD_STATUS: '/app/api/redlife/common/apply_record_status', // 门店管理 -> 解绑查询
    POI_QUERY_SHOP_AUDIT_LIST: '/app/api/redlife/poi/query_shop_audit_list', // 门店管理 -> 审核历史
    REDLIFE_CHECK_CREATED_MERCHANT: '/app/api/redlife/check_created_merchant', // 查看是否开通店铺
    QUERY_SHOP_SUPPORT_CREATED_MERCHANT: '/app/api/redlife/query_shop_support_created_merchant', // 查询shop是否支持开通本地店铺

    // ==========门店认领==========
    API_REDLIFE: '/app/api/redlife', // 门店认领 -> poi是否被认领
    REDLIFE_PRE_CHECK: '/app/api/redlife/pre_check', // 门店认领 -> 获取认领类型
    REDLIFE_MERCHANT_APPLY_QUERY: '/app/api/redlife/merchant/apply/query', // 入驻 -> 查询入驻总进度
    POI_QUERY_CLAIM_INFO: '/app/api/redlife/poi/query_claim_info', // 门店认领 -> 认领门店回显信息查询
    REDLIFE_MRECHANT_CANCLAIM_POI_MERCHANT: '/app/api/redlife/merchant/can_claim_poi_merchant', // 门店认领 -> POI是否可以从认领转入驻，灰度
    REDLIFE_MERCHNT_POI_DRAFT_QUERY: '/app/api/redlife/merchant/poi/draft/query', // 入驻 -> 查询入驻的门店认领信息
    POI_UPSERT_CLAIM_POI: '/app/api/redlife/poi/upsert_claim_poi', // 本地认领 -> 本地门店认领提交
    REDLIFE_MERCHANT_POI_DRAFT_SAVE: '/app/api/redlife/merchant/poi/draft/save', // 入驻 -> 新版认领门店入驻提交
    POI_SUBMIT_APPLY: `${LIFE_OLD_HOST}/api/edith/app/businesscenter/poi/submitapply`, // 轻认领 -> 认领门店轻提交

    // ==========门店装修==========
    POI_GET_BRAND_POI_REVIEWS: `${LIFE_OLD_HOST}/api/edith/app/poi/get_brand_poi_reviews`, // 门店装修 -> 门店信息点查
    REDLIFE_SHOP_BATCH_FIT_UP_SHOP: '/app/api/redlife/shop/batch_fit_up_shop', // 门店装修 -> 新版门店装修提交

    // ==========菜单管理==========
    GET_MENU_UPLOAD_STATUS: '/app/api/redlife/menu/upload_status', // 获取菜单上传状态
    GET_MENU_LIST: '/app/api/redlife/menu/list', // 获取菜单列表
    CREATE_MENU: '/app/api/redlife/menu/create', // 创建菜单
    UPDATE_MENU: '/app/api/redlife/menu/update', // 更新菜单
    DELETE_MENU: '/app/api/redlife/menu/delete', // 删除菜单
    UPLOAD_MENU_PHOTO: '/app/api/redlife/menu/upload_photo', // 上传菜单照片
    RECOGNIZE_MENU: '/app/api/redlife/menu/recognize', // 识别菜单内容

  },
  BASE_CONFIG: {
    defaults: {
      timeout: process.env.BROWSER ? 10000 : 3000,
      useBridge: true,
      preferBridge: true,
      headers: {
        'Content-Type': 'application/json',
      },
    },
    development: {
      withCredentials: true,
    },
  },
}

export default httpConfig
