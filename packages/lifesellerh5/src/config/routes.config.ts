import { LauncherOptions } from '@xhs/launcher'
import { commonChecks } from '@xhs/ozone-detector'
import tracker from '@xhs/protobuf-pages-lifeseller-tracker/tracker_template'
import parseUrl from 'shared/utils/parseUrl'
import Main from '../pages/Main.vue'
import Redirect from '../pages/Redirect.vue'
// 数据
import ROUTE_NAME from '~/constants/routename'

export const ROUTE_PREFIX = '/lifesellerh5/'

const routes: LauncherOptions['routes'] = [
  {
    // 默认 route 路径
    path: '/lifesellerh5',
    component: Main,
  },
  {
    // 自动重定向路由
    path: '/lifesellerh5/redirect',
    component: Redirect,
  },
  // 探店博主详情
  {
    path: '/lifesellerh5/kol-detail',
    name: 'KolDetail',
    component: () => import('../pages/KolSquareDetail/index.vue'),
    meta: {
      pageInstance: 'store_blogger_square_profile',
      pageKey: 'store_blogger_square_profile',
      getTrackerData: route => {
        const {
          source, city_name, kol_user_id,
        } = route.query
        return tracker[81734]({
          source,
          cityName: decodeURIComponent(city_name as string),
          bloggerId: kol_user_id as string,
        })
      },
      getPageEnd: route => {
        const {
          source, city_name, kol_user_id
        } = route.query
        return tracker[81735]({
          source,
          cityName: decodeURIComponent(city_name as string),
          bloggerId: kol_user_id as string,
        })
      }
    }
  },
  // POI反馈页面
  {
    path: '/lifesellerh5/errorfeedback',
    name: 'ErrorFeedback',
    component: () => import('../pages/ErrorFeedback/index.vue'),
    meta: {
      pageInstance: 'poi_error_page',
      getTrackerData: route => {
        const { poiId, poiSessionId } = route.query
        return tracker[49344]({
          poiId,
          poiSessionId
        })
      },
      getPageEnd: route => {
        const { poiId, poiSessionId } = route.query
        return tracker[49345]({
          poiId,
          poiSessionId
        })
      }
    },
  },
  /** ********* 数据中心 ********* */
  {
    path: '/lifesellerh5/myPoi/dataCenter',
    name: ROUTE_NAME.POI_DATA_CENTER,
    component: () => import('../pages/claimPoi/myPoi/dataCenter/index.vue'),
    meta: {
      pageInstance: 'claim_poi_data_center_page',
    },
  },
  // 门店结果页面
  {
    path: '/lifesellerh5/claimPoi/result',
    name: ROUTE_NAME.CLAIM_STORE_RESULT,
    component: () => import('../pages/claimPoi/result/index.vue'),
  },
  {
    path: '/lifesellerh5/bindCardConfirm',
    redirect: '/lifesellerh5/bindCardConfirm/validate',
    children: [
      {
        path: '/lifesellerh5/bindCardConfirm/validate',
        component: () => import('../pages/BindCardConfirm/Validate/index.vue'),
        name: 'BindCardConfirmValidate',
      },
      {
        path: '/lifesellerh5/bindCardConfirm/confirm',
        component: () => import('../pages/BindCardConfirm/Confirm/index.vue'),
        name: 'BindCardConfirm',
      },
      {
        path: '/lifesellerh5/bindCardConfirm/result',
        component: () => import('../pages/BindCardConfirm/Result/index.vue'),
        name: 'BindCardConfirmResult',
      },
    ]
  },
  /** ********* 上传成功页面 ********* */
  {
    name: 'uploadSuccess',
    path: '/lifesellerh5/upload-success',
    component: () => import('~/pages/success/UploadSuccess.vue'),
    meta: {
      title: '拍照上传',
      pageKey: 'upload_success',
    },
  },
  {
    name: ROUTE_NAME.IMAGE_CROPPER,
    path: '/lifesellerh5/image-cropper',
    component: () => import('~/pages/ImageCropper/index.vue'),
    meta: {
      title: '图片裁剪',
      pageKey: 'image_cropper',
    },
  },
  /** ********* 身份证的微信上传页面 ********* */
  {
    name: 'identityWechatUpload',
    path: '/lifesellerh5/identity-card/wechat-upload',
    component: () => import('~/pages/wechat-upload/identity-card/WechatUpload.vue'),
    meta: {
      title: '拍照上传',
      desc: '给PC端微信上传组件使用的身份证上传h5页面',
      pageKey: 'identity_wechat_upload',
      attributes() {
        const { source, fieldName = '', sellerType = '' } = parseUrl()
        return Promise.resolve()
          .then(() => ({
            settle_from: source,
            fieldName,
            sellerType,
          }))
      },
    },
  },
  /** ********* 港澳台通行证的微信上传页面 ********* */
  {
    name: 'passWechatUpload',
    path: '/lifesellerh5/pass/wechat-upload',
    component: () => import('~/pages/wechat-upload/pass-certificate/WechatUpload.vue'),
    meta: {
      title: '拍照上传',
      desc: '给PC端微信上传组件使用的港澳通行证h5页面',
      pageKey: 'pass_wechat_upload',
      attributes() {
        const { source, fieldName = '', sellerType = '' } = parseUrl()
        return Promise.resolve()
          .then(() => ({
            settle_from: source,
            fieldName,
            sellerType,
          }))
      },
    },
  },
  /** ********* 多文件的微信上传页面 ********* */
  {
    name: 'wechatUpload',
    path: '/lifesellerh5/wechat-upload',
    component: () => import('~/pages/wechat-upload/index.vue'),
    meta: {
      title: '拍照上传',
      desc: '给PC端微信上传组件使用的多文件上传h5页面',
      pageKey: 'common_wechat_upload',
      attributes() {
        const { source, fieldName = '', sellerType = '' } = parseUrl()
        return Promise.resolve()
          .then(() => ({
            settle_from: source,
            fieldName,
            sellerType,
          }))
      },
    },
  },
  {
    name: 'faceRecognition',
    path: '/lifesellerh5/face-recognition/redirect',
    component: () => import('~/pages/FaceRecognition/index.vue'),
    meta: {
      title: '人脸识别',
      pageKey: 'h5_face_recognition',
      attributes() {
        const { source, fieldName = '', sellerType = '' } = parseUrl()
        return Promise.resolve()
          .then(() => ({
            face_platform: commonChecks.isXHS ? 'XHS' : 'WECHAT',
            settle_from: source,
            fieldName,
            sellerType,
          }))
      },
    }
  },
  /** ********* 门店管理 ********* */
  {
    path: '/lifesellerh5/myPoi',
    name: ROUTE_NAME.MY_POI,
    component: () => import('../pages/claimPoi/myPoi/index.vue'),
    meta: {
      title: '门店管理',
      pageInstance: 'claim_poi_page',
    },
  },
  /** ********* 审核历史 ********* */
  {
    path: '/lifesellerh5/myPoi/auditHistory',
    name: ROUTE_NAME.POI_AUDIT_HISTORY,
    component: () => import('../pages/claimPoi/myPoi/auditHistory/index.vue'),
    meta: {
      pageInstance: 'claim_poi_audit_history_page',
    },
  },
  /** ********* 认领门店 ********* */
  {
    name: ROUTE_NAME.CLAIM_SHOP,
    path: `${ROUTE_PREFIX}claim-store-page`,
    component: () => import('~/pages/ClaimShopPage/index.vue'),
    meta: {
      title: '认领门店',
      hideRightIcon: true,
      // pageKey: 'h5_face_recognition',
      // attributes() {
      //   const { source, fieldName = '', sellerType = '' } = parseUrl()
      //   return Promise.resolve()
      //     .then(() => ({
      //       face_platform: commonChecks.isXHS ? 'XHS' : 'WECHAT',
      //       settle_from: source,
      //       fieldName,
      //       sellerType,
      //     }))
      // },
    }
  },
  /** ********* 门店选择 ********* */
  {
    name: ROUTE_NAME.STORE_SEARCH,
    path: '/lifesellerh5/store-search/detail',
    component: () => import('~/pages/StoreSearch/index.vue'),
    meta: {
      title: '选择门店',
      pageKey: 'h5_store_search',
      attributes() {
        // const { source, fieldName = '', sellerType = '' } = parseUrl()
        // return Promise.resolve()
        //   .then(() => ({
        //     face_platform: commonChecks.isXHS ? 'XHS' : 'WECHAT',
        //     settle_from: source,
        //     fieldName,
        //     sellerType,
        //   }))
      },
    }
  },
  /** ********* 门店信息 ********* */
  {
    name: ROUTE_NAME.SHOP_INFO,
    path: `${ROUTE_PREFIX}shop-info`,
    component: () => import('~/pages/ShopInfoPage/index.vue'),
    meta: {
      title: '门店信息',
      hideRightIcon: true,
      // pageKey: 'h5_face_recognition',
      // attributes() {
      //   const { source, fieldName = '', sellerType = '' } = parseUrl()
      //   return Promise.resolve()
      //     .then(() => ({
      //       face_platform: commonChecks.isXHS ? 'XHS' : 'WECHAT',
      //       settle_from: source,
      //       fieldName,
      //       sellerType,
      //     }))
      // },
    },
  },
  /** ********* 法人信息页面 - 认领 ********* */
  {
    name: ROUTE_NAME.LEGAL_PERSON_STORE_CLAIM,
    path: `${ROUTE_PREFIX}legal-person-claim`,
    component: () => import('~/pages/LegalPerson/claim/index.vue'),
    meta: {
      title: '提交材料',
      hideRightIcon: true,
    },
  },
  /** ********* 法人信息页面 - 独立入驻 ********* */
  {
    name: ROUTE_NAME.LEGAL_PERSON_SETTLE,
    path: `${ROUTE_PREFIX}legal-person-settle`,
    component: () => import('~/pages/LegalPerson/settle/index.vue'),
    meta: {
      title: '提交材料',
      hideRightIcon: true,
    },
  },

  /** ********* 营业执照页面 - 门店管理 ********* */
  {
    name: ROUTE_NAME.BUSINESS_LICENSE_STORE_MANAGE,
    path: `${ROUTE_PREFIX}business-license-store-manage`,
    component: () => import('~/pages/BusinessLicense/storeManage/index.vue'),
    meta: {
      title: '提交材料',
      hideRightIcon: true,
    },
  },
  /** ********* 营业执照页面 - 独立入驻 ********* */
  {
    name: ROUTE_NAME.BUSINESS_LICENSE_SETTLE,
    path: `${ROUTE_PREFIX}business-license-settle`,
    component: () => import('~/pages/BusinessLicense/settle/index.vue'),
    meta: {
      title: '提交材料',
      hideRightIcon: true,
    },
  },
  /** ********* 门店装修页面 ********* */
  {
    name: ROUTE_NAME.SHOP_DECORATE,
    path: `${ROUTE_PREFIX}shop-decorate`,
    component: () => import('~/pages/ShopDecorate/index.vue'),
    meta: {
      title: '门店装修',
      hideRightIcon: true,
      // pageKey: 'h5_face_recognition',
      // attributes() {
      //   const { source, fieldName = '', sellerType = '' } = parseUrl()
      //   return Promise.resolve()
      //     .then(() => ({
      //       face_platform: commonChecks.isXHS ? 'XHS' : 'WECHAT',
      //       settle_from: source,
      //       fieldName,
      //       sellerType,
      //     }))
      // },
    }
  },
  /** ********* 预览门店页面 ********* */
  {
    name: ROUTE_NAME.SHOP_PREVIEW,
    path: `${ROUTE_PREFIX}shop-preview`,
    component: () => import('~/pages/ShopPreview/index.vue'),
    meta: {
      title: '预览门店',
      hideRightIcon: true,
      // pageKey: 'h5_face_recognition',
      // attributes() {
      //   const { source, fieldName = '', sellerType = '' } = parseUrl()
      //   return Promise.resolve()
      //     .then(() => ({
      //       face_platform: commonChecks.isXHS ? 'XHS' : 'WECHAT',
      //       settle_from: source,
      //       fieldName,
      //       sellerType,
      //     }))
      // },
    }
  },
  {
    // 补充资质-认领
    name: ROUTE_NAME.SUPPLEMENTARY_QUALIFICATIONS_STORE_CLAIM,
    path: `${ROUTE_PREFIX}supplementary-qualifications-store-claim`,
    component: () => import('~/pages/SupplementaryQualifications/claim.vue'),
    meta: {
      title: '补充行业资质',
      hideRightIcon: true,
    }
  },
  {
    // 补充资质-独立入驻
    name: ROUTE_NAME.SUPPLEMENTARY_QUALIFICATIONS_SETTLE,
    path: `${ROUTE_PREFIX}supplementary-qualifications-settle`,
    component: () => import('~/pages/SupplementaryQualifications/settle.vue'),
    meta: {
      title: '提交材料',
      hideRightIcon: true,
    }
  },
  {
    // 审核结果-独立入驻
    name: ROUTE_NAME.AUDIT_RESULTS_SETTLE,
    path: `${ROUTE_PREFIX}audit-results-settle`,
    component: () => import('~/pages/AuditResultsForSettle/Index.vue'),
    meta: {
      title: '审核状态',
      hideRightIcon: true,
    }
  },
  {
    name: ROUTE_NAME.AUDIT_RESULTS,
    path: `${ROUTE_PREFIX}audit-results`,
    component: () => import('~/pages/AuditResults/Index.vue'),
    meta: {
      title: '审核状态',
      hideRightIcon: true,
    }
  },
]

export default routes
