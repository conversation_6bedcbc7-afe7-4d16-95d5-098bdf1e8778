import '@xhs/cube-style'
import '@xhs/riant/lib/index.css'

import Launcher from '@xhs/launcher'
import authPlugin from '@xhs/launcher-plugin-auth'
import insight from '@xhs/apm-insight'
import eagletPlugin from '@xhs/launcher-plugin-eaglet'
import TrackPlugin from '@xhs/launcher-plugin-tracker'
import antiSpamPlugin from '@xhs/launcher-plugin-anti-spam'
import storePlugin from '@xhs/launcher-plugin-store'
import { getUserInfoV2 } from '@xhs/ozone-bridge'
import { env } from 'shared/config/env.config'
import { getBrowserId } from 'shared/utils/browserId'

import authConfig from './config/auth.config'
import httpConfig from './config/http.config'
import routesConfig from './config/routes.config'
import store from './store'

const app = new Launcher('#app', {
  routes: routesConfig,
  http: httpConfig,
})

// TODO: 调试时候开，在所有插件之前添加 sid 保持逻辑
// app.router.beforeEach((to, from, next) => {
//   // 如果来源路由有 sid 参数但目标路由没有
//   if (from.query?.sid && !to.query?.sid) {
//     next({
//       path: to.path,
//       query: {
//         ...to.query,
//         sid: from.query.sid,
//       },
//     })
//   } else {
//     next()
//   }
// })

app.use(authPlugin, authConfig)
app.use(antiSpamPlugin)
app.use(storePlugin, store)

app.use(TrackPlugin, {
  appId: 77,
  attributes: {},
  getUserInfo() {
    return getUserInfoV2()
      .then(result => {
        const userId = result?.value?.userId || getBrowserId()

        return {
          userId,
          groupUserId: userId,
        }
      })
      .catch(() => ({
        userId: '',
        groupUserId: '',
      }))
  }
})

app.use(eagletPlugin, {
  // 业务打点
  tracker: () => import('@xhs/protobuf-pages-lifeseller-tracker'),
  // 仅在生产环境开启native通道
  // enableNativeEmitter: process.env.BUILD_ENV === 'production',
  // 默认关闭页面流量来源特性，需要设置 true 打开
  referPath: false,
  apm: false,
})

insight.init({
  jsError: true,
  env: env === 'production' ? 'production' : 'development',
  app, // 注册监听 vue error 报错的
  Launcher,
})

app.start()

export default app
