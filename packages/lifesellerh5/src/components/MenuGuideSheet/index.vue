<template>
  <ActionSheet
    :show="visible"
    :title="config.title"
    :closable="true"
    class="menu-guide-sheet"
    @close="handleClose"
    @cancel="handleClose"
    @maskClick="handleClose"
  >
    <div class="guide-content">
      <!-- 提示文案 -->
      <div class="description">
        <Text
          type="T2"
          line-height="22px"
          color="rgba(51, 51, 51, 0.8)"
        >
          {{ config.description }}
        </Text>
      </div>
      
      <!-- 示例图片展示区域 -->
      <div class="example-image">
        <img
          :src="config.exampleImageUrl"
          alt="菜单示例"
          class="example-img"
          @error="handleImageError"
        />
      </div>
      
      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <Button
          type="primary"
          class="action-button photo-button"
          @click="handlePhotoGenerate"
        >
          <Icon name="camera" class="button-icon" />
          {{ config.actions.photoText }}
        </Button>
        
        <Button
          type="default"
          class="action-button manual-button"
          @click="handleManualInput"
        >
          <Icon name="edit" class="button-icon" />
          {{ config.actions.manualText }}
        </Button>
      </div>
    </div>
  </ActionSheet>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ActionSheet, Button, Text, Icon } from '@xhs/reds-h5-next'
import { MENU_CONFIG } from '../../services/menu'
import type { GuideSheetConfig } from '../../types/menu'

// Props 定义
interface Props {
  /** 是否显示面板 */
  visible: boolean
  /** 自定义配置，不传则使用默认配置 */
  config?: Partial<GuideSheetConfig>
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  config: () => ({})
})

// Events 定义
const emit = defineEmits<{
  close: []
  photoGenerate: []
  manualInput: []
}>()

// 计算属性
const config = computed(() => ({
  ...MENU_CONFIG.GUIDE_SHEET,
  ...props.config
}))

// 方法
const handleClose = () => {
  emit('close')
}

const handlePhotoGenerate = () => {
  emit('photoGenerate')
  handleClose()
}

const handleManualInput = () => {
  emit('manualInput')
  handleClose()
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 图片加载失败时使用默认占位图
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04NSA0NUg5NVY1NUg4NVY0NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPHA+dGggZD0iTTc1IDY1SDEyNVY3NUg3NVY2NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'
}
</script>

<style scoped lang="stylus">
.menu-guide-sheet
  .guide-content
    padding 20px 16px 32px
    
    .description
      margin-bottom 24px
      text-align center
      line-height 1.5
      
    .example-image
      margin-bottom 32px
      text-align center
      
      .example-img
        width 100%
        max-width 280px
        height auto
        border-radius 8px
        box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
        
    .action-buttons
      display flex
      flex-direction column
      gap 12px
      
      .action-button
        width 100%
        height 48px
        border-radius 8px
        font-size 16px
        font-weight 500
        display flex
        align-items center
        justify-content center
        gap 8px
        
        .button-icon
          font-size 18px
          
        &.photo-button
          background-color #ff2442
          border-color #ff2442
          color white
          
          &:active
            background-color #e6203a
            
        &.manual-button
          background-color transparent
          border-color #e5e5e5
          color #333
          
          &:active
            background-color #f5f5f5
            
  // 响应式适配
  @media (max-width: 375px)
    .guide-content
      padding 16px 12px 24px
      
      .example-image
        margin-bottom 24px
        
        .example-img
          max-width 240px
          
      .action-buttons
        .action-button
          height 44px
          font-size 15px
</style>
