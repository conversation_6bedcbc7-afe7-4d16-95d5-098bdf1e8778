import {
  ref, computed, ComputedRef, Ref
} from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

import { ValidityTimePickerValue } from '../common/ValidityTimePicker/types'

// 证件类型枚举
export enum IdType {
  ID_CARD = 'ID_CARD',
  PASSPORT = 'PASSPORT',
  HONG_KONG_MACAU_PASS = 'HONG_KONG_MACAU_PASS',
  TAIWAN_PASS = 'TAIWAN_PASS',
  FOREIGN_PASSPORT = 'FOREIGN_PASSPORT'
}

// 法人信息数据类型
export interface BaseLegalPersonData {
  name: string
  idType: string
  idNumber: string
  idImages: any[]
  validityPeriod: ValidityTimePickerValue | null // 有效期
  [key: string]: any
}
// 默认使用基础类型
export type LegalPersonData = BaseLegalPersonData

// 通用表单返回类型
export interface BaseLegalPersonFormReturn<T extends BaseLegalPersonData = LegalPersonData>{
  formData: Ref<T>
  formRules: ComputedRef<Record<string, any>>
  isPreview: ComputedRef<boolean>
  poiId: ComputedRef<string>
  isIdParsed: Ref<boolean>

  // 证件类型相关
  idTypeSheetVisible: Ref<boolean>
  idTypeText: ComputedRef<string>
  idTypePickerColumns: ComputedRef<any[]>
  idTypePickerValue: ComputedRef<string[]>
  idNumberLabel: ComputedRef<string>
  needsBackImage: ComputedRef<boolean>

  showIdTypeSheet: () => void
  hideIdTypeSheet: () => void
  confirmIdTypePicker: (value: string[]) => void
  handleInfoExtracted: (info: any) => void
}

// 通用配置选项
interface LegalPersonFormOptions<T extends BaseLegalPersonData = LegalPersonData> {
  initialData?: Partial<T>
}

// 证件类型选项
const ID_TYPE_OPTIONS = [
  { value: IdType.ID_CARD, label: '身份证' },
  // { value: IdType.PASSPORT, label: '护照' },
  // { value: IdType.HONG_KONG_MACAU_PASS, label: '港澳通行证' },
  // { value: IdType.TAIWAN_PASS, label: '台湾通行证' },
  // { value: IdType.FOREIGN_PASSPORT, label: '外国护照' }
]

// 创建默认法人表单数据的工厂函数
function createDefaultLegalPersonFormData<T extends BaseLegalPersonData>(
  initialData?: Partial<T>,
  userName?: string
): T {
  return {
    name: userName || '',
    idType: IdType.ID_CARD, // 默认身份证
    idNumber: '',
    idImages: [],
    validityPeriod: null,
    ...initialData
  } as T
}

export function useBaseLegalPersonForm<T extends BaseLegalPersonData = LegalPersonData>(
  options: LegalPersonFormOptions<T> = {}
): BaseLegalPersonFormReturn<T> {
  const route = useRoute()
  const store = useStore()

  // 状态判断
  const isPreview = computed(() => Boolean(route.query.isPreview))
  const poiId = computed(() => route.query.poiId as string)

  // 每个组件实例都有独立的状态 - 简单且有效
  const formData = ref(createDefaultLegalPersonFormData<T>(
    options.initialData,
    store.state.ShopUserInfoStore?.userName
  )) as Ref<T>
  const isIdParsed = ref(false)
  const idTypeSheetVisible = ref(false)

  // 基础表单验证规则
  const formRules = computed(() => ({
    idImages: [{
      // type: 'array',
      required: true,
      validator: (_rule: any, val: Record<string, any>[]) => {
        // console.log('val', val, formData.value.idType, IdType.ID_CARD)
        if (!Array.isArray(val) || val.length === 0) {
          return false
        }
        // 身份证需要正反两面
        if (formData.value.idType === IdType.ID_CARD) {
          return val.length >= 2 && val.every(img => img && (img?.url || img?.file))
        }

        // 其他证件类型只需要一面
        return val.length >= 1 && val[0] && val[0]?.url
      },
      message: () => {
        if (formData.value.idType === IdType.ID_CARD) {
          return '请上传身份证正反面照片'
        }
        return '请上传证件照片'
      }
    }],
    idNumber: [
      {
        type: 'string',
        required: true,
        validator: (_rule: any, val: string) => val?.trim() !== '',
        message: `请输入${idNumberLabel.value}`,
      },
    ],
    validityPeriod: [{
      type: 'object',
      required: true,
      validator: (_rule: any, val: ValidityTimePickerValue|null) => {
        if (!val || typeof val !== 'object') return false
        if (val.qualValidityPeriod === 0) return true // 永久有效
        return !!(val.startTime && val.endTime)
      },
      message: '请选择有效期'
    }]
  }))

  // 证件类型相关计算属性
  const idTypeText = computed(() => {
    const option = ID_TYPE_OPTIONS.find(item => item.value === formData.value.idType)
    return option?.label || ''
  })

  const idTypePickerColumns = computed(() => [
    ID_TYPE_OPTIONS.map(option => ({
      label: option.label,
      value: option.value
    }))
  ])

  const idTypePickerValue = computed(() => [formData.value.idType])

  // 证件号码标签
  const idNumberLabel = computed(() => {
    switch (formData.value.idType) {
      case IdType.ID_CARD:
        return '身份证号码'
      case IdType.PASSPORT:
        return '护照号码'
      case IdType.HONG_KONG_MACAU_PASS:
        return '港澳通行证号码'
      case IdType.TAIWAN_PASS:
        return '台湾通行证号码'
      case IdType.FOREIGN_PASSPORT:
        return '外国护照号码'
      default:
        return '证件号码'
    }
  })

  // 是否需要背面照片
  const needsBackImage = computed(() => formData.value.idType === IdType.ID_CARD)

  // 显示证件类型选择器
  const showIdTypeSheet = () => {
    if (isPreview.value) return
    idTypeSheetVisible.value = true
  }

  // 隐藏证件类型选择器
  const hideIdTypeSheet = () => {
    idTypeSheetVisible.value = false
  }

  // 确认证件类型选择
  const confirmIdTypePicker = (value: string[]) => {
    const oldIdType = formData.value.idType
    formData.value.idType = value[0]

    // 如果证件类型改变，清空相关数据
    if (oldIdType !== value[0]) {
      formData.value.idNumber = ''
      formData.value.idImages = []
    }

    hideIdTypeSheet()
  }

  // 处理身份证信息解析
  const handleInfoExtracted = (info: any) => {
    // console.log('handleInfoExtracted', info)
    // 基础信息自动填充
    // 经过确认，OCR识别不回填姓名
    // if (info.name) {
    //   formData.value.name = info.name
    // }
    if (info.idNumber) {
      formData.value.idNumber = info.idNumber
    }

    // 扩展字段让业务层处理，保持基础层纯净

    // 处理有效期信息
    if (info.validityPeriod) {
      formData.value.validityPeriod = {
        qualValidityPeriod: info.validityPeriod.qualValidityPeriod || 1,
        startTime: info.validityPeriod.startTime || 0,
        endTime: info.validityPeriod.endTime || 0
      }
    }

    isIdParsed.value = true
  }

  return {
    // 数据
    formData,
    formRules,
    isPreview,
    poiId,
    isIdParsed,

    // 证件类型相关
    idTypeSheetVisible,
    idTypeText,
    idTypePickerColumns,
    idTypePickerValue,
    idNumberLabel,
    needsBackImage,

    // 方法
    showIdTypeSheet,
    hideIdTypeSheet,
    confirmIdTypePicker,
    handleInfoExtracted
  }
}
