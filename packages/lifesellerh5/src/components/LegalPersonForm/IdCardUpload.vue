<template>
  <div class="business-license-upload">
    <div class="upload-container">
      <!-- 上传提示 -->
      <div v-if="userName && !hasValidFiles" class="upload-title">请上传"{{ userName }}"的有效证件</div>
      <!-- 上传按钮 -->
      <Uploader
        ref="uploaderRef"
        v-model="fileList"
        :disable="isDisabled || uploading"
        :accept="accept"
        :max-size="maxSize"
        :type="UploaderType.UploaderType.IDENTITY"
        :identity-country-icon="Picture"
        :identity-user-icon="MeB"
        :style="{
          backgroundColor: 'rgba(48, 48, 52, 0.05) !important',
        }"
        @success="handleUploadSuccess"
        @fail="handleUploadError"
        @delete="handleDelete"
      >
      </Uploader>
    </div>

    <!-- 上传状态指示器 -->
    <div v-if="uploading">
      <LoadingNext />
    </div>

    <!-- OCR识别结果提示 -->
    <div v-if="hasFile && ocrResult" class="ocr-result-tips">
      <div v-if="ocrResult.success" class="success-tip">
        <span>{{ ocrResult.message }}</span>
      </div>
      <div v-else class="error-tip">
        <span>{{ ocrResult.message }}</span>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue'
  import {
    Uploader,
    UploaderType,
    showToast,
    ToastType
  } from '@xhs/reds-h5-next'
  import LoadingNext from '../loading-next/index.vue'

  import { uploadImageNew } from '~/utils/upload/imgUpload'

  import {
    postIdcardOcr,
    IOcrImage
  } from '~/services/edith_post_idcard_ocr'

  // 静态资源
  import '~/assets/svg/addB.svg'
  import '~/assets/svg/bannerCloseC.svg'
  import Picture from '~/assets/svg/picture.svg'
  import MeB from '~/assets/svg/me_b.svg'

  interface Props {
    modelValue: any[]
    isDisabled?: boolean
    userName?: string
  }

  interface IfileAttachmentList {
    fileName?: string
    fileType?: string
    height?: number
    width?: number
    uploaderInfoModel: {
      fileId?: string
      url: string
      scene?: string
      bizName?: string
      isSecret?: boolean
      cloudType?: number
    }
  }

  interface IdCardInfo {
    name?: string
    idNumber?: string
    address?: string
    validityPeriod?: {
      startTime?: number
      endTime?: number
      qualValidityPeriod?: number
    }
    issuingAuthority?: string
    fileAttachmentList?: IfileAttachmentList[]
  }

  interface Emits {
    (e: 'update:model-value', value: any[]): void
    (e: 'info-extracted', info: IdCardInfo): void
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ([]),
    isDisabled: false,
    userName: ''
  })

  const emit = defineEmits<Emits>()

  // 上传配置
  const uploaderRef = ref<any>(null)
  const uploading = ref(false)
  const accept = 'image/jpg,image/png,image/jpeg'
  const maxSize = 5 * 1024 * 1024 // 5MB
  const ocrResult = ref<{ success: boolean; message: string } | null>(null)
  const frontOcrResult = ref<any>(null) // 正面OCR结果
  const backOcrResult = ref<any>(null) // 背面OCR结果

  const fileList = computed({
    get() {
      const result: any[] = [{}, {}]

      if (props.modelValue?.[0]?.url && props.modelValue[0]?.fileId) {
        result[0] = {
          url: props.modelValue[0]?.url,
          status: 'success',
          desc: '身份证正面'
        }
      }

      if (props.modelValue?.[1]?.url && props.modelValue[1]?.fileId) {
        result[1] = {
          url: props.modelValue[1]?.url,
          status: 'success',
          desc: '身份证背面'
        }
      }
      return result
    },
    set(newFileList: any[]) {
      // 修复数据同步问题：保持固定数组结构
      const currentFiles = [...(props.modelValue || [{}, {}])]
      // 确保数组长度为2
      while (currentFiles.length < 2) {
        currentFiles.push({})
      }

      // 处理新上传的文件
      newFileList.forEach((newFile: any) => {
        if (newFile && (newFile.url || newFile.content)) {
          // 找到应该放置的位置
          let targetIndex = -1

          // 先查找是否有空位
          for (let i = 0; i < currentFiles.length; i++) {
            if (!currentFiles[i] || !currentFiles[i].url) {
              targetIndex = i
              break
            }
          }

          // 如果没有空位，追加到末尾
          if (targetIndex === -1) {
            targetIndex = currentFiles.length
          }

          // 确保数组有足够长度
          while (currentFiles.length <= targetIndex) {
            currentFiles.push({})
          }

          // 放置新文件
          currentFiles[targetIndex] = {
            url: newFile.url || newFile.content,
            desc: targetIndex === 0 ? '身份证人像面' : '身份证国徽面',
            fileName: newFile.fileName,
            fileType: newFile.fileType,
            height: newFile.height,
            width: newFile.width,
            fileId: newFile.fileId,
            bizName: newFile.bizName,
            scene: newFile.scene,
            isSecret: newFile.isSecret,
            cloudType: newFile.cloudType
          }
        }
      })

      // 保持固定数组结构，不过滤空位
      emit('update:model-value', currentFiles)
    }
  })
  const hasFile = computed(() => fileList.value.length > 0)

  // 检查是否有有效文件
  const hasValidFiles = computed(() => Array.isArray(props.modelValue) && props.modelValue.some(file => file?.url))

  // 监听 modelValue 变化
  watch(() => props.modelValue, newValue => {
    if (!newValue || newValue.length === 0) {
      ocrResult.value = null
      frontOcrResult.value = null
      backOcrResult.value = null
    }
  }, { immediate: true, deep: true })

  // 处理上传成功
  const handleUploadSuccess = async (_file: any) => {
    try {
      uploading.value = true
      // 找到有文件的项及其位置索引
      const fileIndex = _file.findIndex((item: any) => item && item.file)
      if (fileIndex === -1) {
        throw new Error('未找到上传的文件')
      }

      const targetIndex = fileIndex // 直接使用下标：0= 人像面 ，1=国徽

      // 上传文件到服务器
      const promises = _file.map((item: any) => {
        if (item.file) {
          return uploadImageNew(item.file, maxSize || 0).then((res: any) => res)
        }
        return null
      }).filter(Boolean)
      const uploadResult: any = await Promise.all(promises)
      if (!uploadResult[0]) {
        throw new Error('上传失败，请重新上传')
      }
      // 直接将图片放置到对应位置并进行OCR识别
      await processImageAtPosition(uploadResult[0], targetIndex)
    } catch (error: any) {
      handleUploadError(error)
    } finally {
      uploading.value = false
    }
  }

  // 处理图片放置到指定位置
  const processImageAtPosition = async (uploadResult: any, targetIndex: number) => {
    try {
      // const sideType = targetIndex === 0 ? '人像面' : '国徽面'

      // 直接将图片放置到指定位置
      await placeImageAtPosition(uploadResult, targetIndex)

      // 进行OCR识别（仅用于信息提取）
      await performOCRForInformationExtraction(uploadResult, targetIndex)
    } catch (error: any) {
      ocrResult.value = {
        success: false,
        message: `上传失败: ${error.message || '处理图片失败'}`
      }
      throw error
    }
  }

  // 将图片放置到指定位置
  const placeImageAtPosition = async (uploadResult: any, targetIndex: number) => {
    const currentFiles = [...(props.modelValue || [{}, {}])]
    const sideType = targetIndex === 0 ? '人像面' : '国徽面'

    // 确保数组有足够长度
    while (currentFiles.length < 2) {
      currentFiles.push({})
    }
    // 构建文件对象
    const fileObject = {
      url: uploadResult.url,
      desc: `身份证${sideType}`,
      qualificationCode: uploadResult.qualificationCode,
      qualificationName: uploadResult.qualificationName,
      fileName: uploadResult.fileName,
      fileType: uploadResult.fileType,
      height: uploadResult.height,
      width: uploadResult.width,
      fileId: uploadResult.fileId,
      bizName: uploadResult.bizName,
      scene: uploadResult.scene,
      isSecret: uploadResult.isSecret,
      cloudType: uploadResult.cloudType
    }

    // 放置到指定位置
    currentFiles[targetIndex] = fileObject
    if (currentFiles.length > 2) {
      // 如果超过2个文件，清除多余的
      currentFiles.splice(2)
    }
    emit('update:model-value', currentFiles)
  }

  // OCR识别用于信息提取
  const performOCRForInformationExtraction = async (uploadResult: any, targetIndex: number) => {
    try {
      const ocrImage: IOcrImage = {
        ocrImageType: targetIndex === 0 ? 'ID_CARD_FRONT' : 'ID_CARD_BACK',
        uploaderInfoModel: {
          bizName: uploadResult.bizName,
          scene: uploadResult.scene,
          fileId: uploadResult.fileId,
          url: uploadResult.url,
          isSecret: uploadResult.isSecret,
          cloudType: uploadResult.cloudType
        }
      }

      const response = await postIdcardOcr({ ocrImage })

      const ocrData = response?.qualificationOCRInfo?.ocrResult

      if (!ocrData || Object.keys(ocrData).length === 0) {
        ocrResult.value = {
          success: false,
          message: 'OCR识别结果为空，请更换图片或手动填写信息'
        }
        emit('info-extracted', {})
        throw new Error('OCR识别结果为空，请检查上传的图片是否清晰')
      }

      // 保存OCR结果
      if (targetIndex === 0) {
        frontOcrResult.value = ocrData
        if (Object.keys(ocrData).length) {
          ocrResult.value = {
            success: true,
            message: '身份证人像面上传成功'
          }
        }
      } else {
        backOcrResult.value = ocrData
        if (Object.keys(ocrData).length) {
          ocrResult.value = {
            success: true,
            message: '身份证国徽面上传成功'
          }
        }
      }

      // 检查是否两张都已识别完成
      checkAndEmitCompleteInfo()
    } catch (error: any) {
      // OCR失败不影响上传，只是无法提取信息
      // console.warn('OCR识别失败:', error.message)
    }
  }

  // 检查并发送完整信息
  const checkAndEmitCompleteInfo = () => {
    // 检查是否两张都已识别完成
    if (frontOcrResult.value && backOcrResult.value) {
      try {
        // 合并OCR结果
        const mergedOcrResult = { ...frontOcrResult.value, ...backOcrResult.value }

        // 根据当前的modelValue构建fileAttachmentList，保持原始索引
        const fileAttachmentItems = props.modelValue
          ?.map((file: any) => {
            if (!file?.url) return null
            return {
              fileName: file.filename || file.fileName || '',
              fileType: file?.fileType,
              height: file?.height,
              width: file?.width,
              uploaderInfoModel: {
                fileId: file?.fileId,
                url: file?.url,
                scene: file?.scene,
                bizName: file?.bizName,
                isSecret: file?.isSecret,
                cloudType: file?.cloudType
              }
            }
          }) || []

        const currentFileAttachmentList = fileAttachmentItems.filter((item): item is NonNullable<typeof item> => Boolean(item))

        // 根据OCR结果构建身份证信息
        const idCardInfo: IdCardInfo = {
          name: mergedOcrResult.name,
          idNumber: mergedOcrResult.number,
          address: mergedOcrResult.address,
          issuingAuthority: mergedOcrResult.issuingAuthority,
          fileAttachmentList: currentFileAttachmentList
        }
        if (mergedOcrResult.permanent !== undefined && mergedOcrResult.permanent !== null) {
          idCardInfo.validityPeriod = {
            startTime: mergedOcrResult.startTime || 0,
            endTime: mergedOcrResult.endTime || 0,
            qualValidityPeriod: mergedOcrResult.permanent ? 0 : 1
          }
        }

        // 如果有其他字段，也添加进去
        Object.keys(mergedOcrResult).forEach(key => {
          if (!idCardInfo[key as keyof IdCardInfo]) {
            (idCardInfo as any)[key] = mergedOcrResult[key]
          }
        })

        ocrResult.value = {
          success: true,
          message: '已成功识别身份证信息，请再次检查确保信息无误'
        }
        // 发送识别结果
        emit('info-extracted', idCardInfo)
      } catch (error: any) {
        ocrResult.value = {
          success: false,
          message: error.message
        }
      }
    }
  }

  // 处理上传错误 - 改进的错误处理和重试机制
  const handleUploadError = (error: any) => {
    uploading.value = false
    ocrResult.value = {
      success: false,
      message: error?.message || '上传失败，请重新尝试'
    }
    showToast({
      type: ToastType.ToastBuiltInType.TEXT,
      message: error?.message || '上传失败，请重新尝试',
      duration: 2000
    })
  }

  // 处理文件删除
  const handleDelete = (file: any) => {
    const currentFiles = [...(props.modelValue || [])]
    // 根据描述判断删除的是正面还是背面
    if (file?.desc === '身份证正面' || file?.desc === '身份证人像面') {
      currentFiles[0] = {}
      frontOcrResult.value = null // 清除正面OCR结果
    } else if (file?.desc === '身份证背面' || file?.desc === '身份证国徽面') {
      currentFiles[1] = {}
      backOcrResult.value = null // 清除背面OCR结果
    } else {
      // 如果描述不匹配，根据URL找到对应的文件
      const fileIndex = currentFiles.findIndex(f => f?.url === file?.url)
      if (fileIndex !== -1) {
        currentFiles[fileIndex] = {}
        if (fileIndex === 0) {
          frontOcrResult.value = null
        } else if (fileIndex === 1) {
          backOcrResult.value = null
        }
      }
    }

    // 保持数组结构，不过滤空位
    emit('update:model-value', currentFiles)

    // 检查是否还有有效文件
    const hasValidFiles = currentFiles.some(file => file?.url)
    if (!hasValidFiles) {
      ocrResult.value = null
      frontOcrResult.value = null
      backOcrResult.value = null
    } else {
      // 如果还有文件，重新检查OCR状态
      ocrResult.value = null
    }
  }

</script>

<style lang="stylus" scoped>
.business-license-upload
  width 100%

// 上传提示信息
.upload-title
  font-size 12px
  color rgba(0, 0, 0, 0.45)

.upload-tips
  margin-top 12px

.tip-item
  display flex
  align-items flex-start
  margin-bottom 4px
  font-size 12px
  line-height 16px
  color rgba(0, 0, 0, 0.45)

  &:last-child
    margin-bottom 0

  .tip-number
    flex-shrink 0
    margin-right 4px

  .tip-content
    flex 1

// OCR结果提示样式
.ocr-result-tips
  margin-top 16px

  .success-tip
    color rgba(0, 0, 0, 0.45)
    font-size 12px
    line-height 17px
    display flex
    align-items center

  .error-tip
    color #FF4D4F
    font-size 12px
    line-height 17px
    display flex
    align-items center
</style>
