<template>
  <div class="menu-manage-container">
    <!-- 菜单管理按钮 -->
    <MenuManageButton
      v-if="visible"
      :button-text="buttonText"
      :button-type="buttonType"
      :class-name="className"
      :loading="loading"
      @click="handleButtonClick"
    />
    
    <!-- 引导半屏面板 -->
    <MenuGuideSheet
      :visible="showGuideSheet"
      :config="guideConfig"
      @close="handleGuideClose"
      @photo-generate="handlePhotoGenerate"
      @manual-input="handleManualInput"
    />
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Toast } from '@xhs/reds-h5-next'
import MenuManageButton from '../MenuManageButton/index.vue'
import MenuGuideSheet from '../MenuGuideSheet/index.vue'
import { mockMenuUploadStatus, MenuStatusChecker, MENU_CONFIG } from '../../services/menu'
import type { MenuManageProps, MenuUploadStatus, GuideSheetConfig } from '../../types/menu'

// Props 定义
interface Props extends MenuManageProps {
  /** 自定义引导面板配置 */
  guideConfig?: Partial<GuideSheetConfig>
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  buttonText: '菜单管理',
  buttonType: 'primary',
  className: '',
  guideConfig: () => ({})
})

// Events 定义
const emit = defineEmits<{
  photoGenerate: []
  manualInput: []
  navigateToList: []
}>()

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const showGuideSheet = ref(false)
const menuStatus = ref<MenuUploadStatus | null>(null)

// 计算属性
const guideConfig = computed(() => ({
  ...MENU_CONFIG.GUIDE_SHEET,
  ...props.guideConfig
}))

// 方法
const loadMenuStatus = async () => {
  try {
    loading.value = true
    // 这里使用 mock 数据，实际项目中应该调用真实的 API
    // const status = await getMenuUploadStatus()
    const status = await mockMenuUploadStatus()
    menuStatus.value = status
  } catch (error) {
    console.error('获取菜单状态失败:', error)
    Toast({
      type: 'fail',
      message: '获取菜单状态失败，请稍后重试'
    })
  } finally {
    loading.value = false
  }
}

const handleButtonClick = async () => {
  if (!menuStatus.value) {
    await loadMenuStatus()
  }
  
  if (!menuStatus.value) {
    return
  }
  
  // 根据菜单状态决定显示内容
  if (MenuStatusChecker.shouldShowGuideSheet(menuStatus.value)) {
    // 显示引导面板
    showGuideSheet.value = true
  } else if (MenuStatusChecker.shouldNavigateToList(menuStatus.value)) {
    // 跳转到菜单管理列表
    handleNavigateToList()
  }
}

const handleGuideClose = () => {
  showGuideSheet.value = false
}

const handlePhotoGenerate = () => {
  emit('photoGenerate')
  // 跳转到拍照生成页面
  router.push(MENU_CONFIG.ROUTES.PHOTO_GENERATE)
}

const handleManualInput = () => {
  emit('manualInput')
  // 跳转到手动输入页面
  router.push(MENU_CONFIG.ROUTES.MANUAL_INPUT)
}

const handleNavigateToList = () => {
  emit('navigateToList')
  // 跳转到菜单管理列表页面
  router.push(MENU_CONFIG.ROUTES.MENU_LIST)
}

// 生命周期
onMounted(() => {
  // 组件挂载时预加载菜单状态
  loadMenuStatus()
})

// 暴露方法给父组件
defineExpose({
  loadMenuStatus,
  showGuideSheet: () => {
    showGuideSheet.value = true
  },
  hideGuideSheet: () => {
    showGuideSheet.value = false
  }
})
</script>

<style scoped lang="stylus">
.menu-manage-container
  position relative
  width 100%
  
  .loading-overlay
    position absolute
    top 0
    left 0
    right 0
    bottom 0
    background-color rgba(255, 255, 255, 0.8)
    display flex
    align-items center
    justify-content center
    z-index 10
    
    .loading-spinner
      width 20px
      height 20px
      border 2px solid #f3f3f3
      border-top 2px solid #ff2442
      border-radius 50%
      animation spin 1s linear infinite
      
@keyframes spin
  0%
    transform rotate(0deg)
  100%
    transform rotate(360deg)
</style>
