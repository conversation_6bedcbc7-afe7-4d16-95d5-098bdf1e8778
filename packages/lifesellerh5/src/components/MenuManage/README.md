# 菜单管理功能

这是一个完整的菜单管理功能实现，包含菜单管理按钮、引导面板、菜单列表、拍照生成和手动输入等功能。

## 功能特性

- ✅ **智能状态判断**: 根据商家菜单上传历史自动判断显示引导面板或跳转列表页面
- ✅ **拍照自动生成**: 支持拍摄实体菜单，AI自动识别菜品名称和价格
- ✅ **手动输入菜单**: 支持手动创建菜单分类和菜品，灵活管理菜单内容
- ✅ **移动端优化**: 完全适配移动端，支持触摸操作和响应式布局
- ✅ **组件化设计**: 模块化组件设计，易于维护和扩展

## 组件结构

```
MenuManage/
├── index.vue                 # 主组件，整合所有功能
├── README.md                 # 使用文档
MenuManageButton/
├── index.vue                 # 菜单管理按钮组件
MenuGuideSheet/
├── index.vue                 # 引导半屏面板组件
```

## 使用方法

### 基础使用

```vue
<template>
  <div>
    <!-- 基础使用 -->
    <MenuManage />
    
    <!-- 自定义按钮文本和类型 -->
    <MenuManage
      button-text="我的菜单"
      button-type="default"
      @photo-generate="handlePhotoGenerate"
      @manual-input="handleManualInput"
      @navigate-to-list="handleNavigateToList"
    />
  </div>
</template>

<script setup>
import MenuManage from '~/components/MenuManage/index.vue'

const handlePhotoGenerate = () => {
  console.log('用户选择拍照生成菜单')
}

const handleManualInput = () => {
  console.log('用户选择手动输入菜单')
}

const handleNavigateToList = () => {
  console.log('跳转到菜单管理列表')
}
</script>
```

### 单独使用按钮组件

```vue
<template>
  <MenuManageButton
    button-text="菜单管理"
    button-type="primary"
    @click="handleClick"
  />
</template>

<script setup>
import MenuManageButton from '~/components/MenuManageButton/index.vue'

const handleClick = () => {
  console.log('菜单管理按钮被点击')
}
</script>
```

### 单独使用引导面板

```vue
<template>
  <MenuGuideSheet
    :visible="showGuide"
    @close="showGuide = false"
    @photo-generate="handlePhotoGenerate"
    @manual-input="handleManualInput"
  />
</template>

<script setup>
import { ref } from 'vue'
import MenuGuideSheet from '~/components/MenuGuideSheet/index.vue'

const showGuide = ref(false)

const handlePhotoGenerate = () => {
  console.log('拍照生成菜单')
}

const handleManualInput = () => {
  console.log('手动输入菜单')
}
</script>
```

## Props 配置

### MenuManage Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | true | 是否显示菜单管理按钮 |
| buttonText | string | '菜单管理' | 按钮文本 |
| buttonType | string | 'primary' | 按钮类型 |
| className | string | '' | 自定义样式类名 |
| guideConfig | object | {} | 自定义引导面板配置 |

### MenuManageButton Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| buttonText | string | '菜单管理' | 按钮文本 |
| buttonType | string | 'primary' | 按钮类型 |
| className | string | '' | 自定义样式类名 |
| loading | boolean | false | 是否显示加载状态 |

### MenuGuideSheet Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | false | 是否显示面板 |
| config | object | {} | 自定义配置 |

## Events 事件

### MenuManage Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| photoGenerate | 用户点击拍照生成菜单 | - |
| manualInput | 用户点击手动输入菜单 | - |
| navigateToList | 跳转到菜单管理列表 | - |

### MenuManageButton Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 按钮点击事件 | - |

### MenuGuideSheet Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭面板 | - |
| photoGenerate | 点击拍照生成 | - |
| manualInput | 点击手动输入 | - |

## 路由配置

菜单管理功能包含以下页面路由：

- `/lifesellerh5/menu/list` - 菜单管理列表页面
- `/lifesellerh5/menu/photo-generate` - 拍照生成菜单页面
- `/lifesellerh5/menu/manual-input` - 手动输入菜单页面
- `/lifesellerh5/menu/demo` - 功能演示页面

## API 接口

菜单管理功能使用以下API接口：

- `GET_MENU_UPLOAD_STATUS` - 获取菜单上传状态
- `GET_MENU_LIST` - 获取菜单列表
- `CREATE_MENU` - 创建菜单
- `UPDATE_MENU` - 更新菜单
- `DELETE_MENU` - 删除菜单
- `UPLOAD_MENU_PHOTO` - 上传菜单照片
- `RECOGNIZE_MENU` - 识别菜单内容

## 数据结构

详细的数据结构定义请参考 `~/types/menu.ts` 文件。

## 样式定制

所有组件都支持通过CSS变量进行样式定制：

```css
:root {
  --primary-color: #ff2442;
  --border-color: #e5e5e5;
  --text-color: #333;
  --danger-color: #ff4757;
}
```

## 注意事项

1. 确保项目中已安装并配置了 `@xhs/reds-h5-next` 组件库
2. 菜单状态判断逻辑目前使用Mock数据，实际使用时需要对接真实API
3. 图片上传和识别功能需要配置相应的后端服务
4. 组件设计遵循移动端优先原则，在桌面端也有良好的显示效果

## 演示页面

访问 `/lifesellerh5/menu/demo` 可以查看完整的功能演示。
