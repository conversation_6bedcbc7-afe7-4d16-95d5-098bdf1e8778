<template>
  <Button
    :type="buttonType"
    :class="['menu-manage-button', className]"
    :loading="loading"
    @click="handleClick"
  >
    {{ buttonText }}
  </Button>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { But<PERSON> } from '@xhs/reds-h5-next'
import type { MenuManageProps } from '../../types/menu'

// Props 定义
interface Props extends MenuManageProps {
  /** 是否显示加载状态 */
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  buttonText: '菜单管理',
  buttonType: 'primary',
  className: '',
  loading: false
})

// Events 定义
const emit = defineEmits<{
  click: []
}>()

// 响应式数据
const loading = ref(false)

// 方法
const handleClick = async () => {
  if (props.loading || loading.value) {
    return
  }
  
  loading.value = true
  try {
    emit('click')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="stylus">
.menu-manage-button
  width 100%
  height 44px
  border-radius 8px
  font-size 16px
  font-weight 500
  
  // 确保按钮在移动端有足够的触摸区域
  min-height 44px
  
  // 适配不同主题色彩
  &.primary
    background-color var(--primary-color, #ff2442)
    border-color var(--primary-color, #ff2442)
    
  &.default
    background-color transparent
    border-color var(--border-color, #e5e5e5)
    color var(--text-color, #333)
    
  &.danger
    background-color var(--danger-color, #ff4757)
    border-color var(--danger-color, #ff4757)
    
  // 禁用状态
  &:disabled
    opacity 0.6
    cursor not-allowed
    
  // 加载状态
  &.loading
    pointer-events none
    
  // 响应式适配
  @media (max-width: 375px)
    font-size 14px
    height 40px
    min-height 40px
</style>
