<template>
  <div class="tabs-red">
    <div ref="tabsContainer" class="tabs-header">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{
          active: activeTab === tab.value
        }"
        @click="handleTabClick(tab.value)"
      >
        <span ref="textRefs" :data-index="index">{{ tab.name }}</span>
      </div>
      <div class="tab-indicator" :style="indicatorStyle"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref, defineProps, defineEmits, watch, nextTick, onMounted
  } from 'vue'

  interface TabItem {
    name: string
    value: string | number
  }

  const props = defineProps<{
    tabs: TabItem[]
    modelValue?: string | number
    defaultActive?: string | number
  }>()

  const emit = defineEmits(['update:modelValue', 'change'])

  const activeTab = ref(props.modelValue || props.defaultActive || (props.tabs.length > 0 ? props.tabs[0].value : ''))
  const tabsContainer = ref<HTMLElement | null>(null)
  const textRefs = ref<HTMLElement[]>([])

  const indicatorStyle = ref({
    width: '0px',
    transform: 'translateX(0px)',
  })

  watch(
    () => props.modelValue,
    newVal => {
      if (newVal !== undefined) {
        activeTab.value = newVal
        updateIndicator()
      }
    }
  )

  watch(
    () => activeTab.value,
    () => {
      updateIndicator()
    }
  )

  const handleTabClick = (value: string | number) => {
    activeTab.value = value
    emit('update:modelValue', value)
    emit('change', value)
  }

  const updateIndicator = () => {
    nextTick(() => {
      const index = props.tabs.findIndex(tab => tab.value === activeTab.value)
      const textEl = textRefs.value[index]
      if (textEl && tabsContainer.value) {
        const containerRect = tabsContainer.value.getBoundingClientRect()
        const textRect = textEl.getBoundingClientRect()
        const offsetLeft = textRect.left - containerRect.left
        indicatorStyle.value = {
          width: `${textEl.offsetWidth}px`,
          transform: `translateX(${offsetLeft}px)`,
        }
      }
    })
  }

  onMounted(() => {
    updateIndicator()
  })
</script>

<style scoped lang="stylus">
.tabs-red
  width: 100%
  height 44px
  display: flex
  align-items: center
  .tabs-header
    display: flex
    position: relative
    width: 100%
    gap 24PX

  .tab-item
    position: relative
    font-size: 14PX
    line-height: 20PX
    color: var(--description)
    cursor: pointer
    text-align: center
    &.active
      color: var(--title)
      font-weight: 500
  .tab-indicator
    position: absolute
    bottom: -4px
    left: 0
    height: 2px
    background-color: #FF2442
    border-radius: 1px
    transition: all 0.3s ease
</style>
