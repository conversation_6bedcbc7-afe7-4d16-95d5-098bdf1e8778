import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import type { ValidityTimePickerValue, ValidityTimePickerProps } from './types'

/**
 * 数据转换函数
 * @param v 原始值
 */
const transformValue = (v?: ValidityTimePickerValue | null) => {
  if (!v) {
    // 没有数据时，返回null表示未选择状态
    return null
  }

  try {
    // 处理时间戳转换，确保正确处理字符串格式的时间戳
    const formatTimestamp = (timestamp: string | number | undefined) => {
      if (!timestamp) return ''

      // 如果是字符串格式的时间戳，先转换为数字
      const numericTimestamp = typeof timestamp === 'string' ? Number(timestamp) : timestamp

      // 验证时间戳是否有效
      if (Number.isNaN(numericTimestamp) || numericTimestamp <= 0) {
        console.warn('formatTimestamp: 无效时间戳', timestamp)
        return ''
      }

      return dayjs(numericTimestamp).format('YYYY-MM-DD')
    }

    return {
      qualValidityPeriod: v.qualValidityPeriod ?? null,
      startTime: formatTimestamp(v.startTime),
      endTime: formatTimestamp(v.endTime),
    }
  } catch (error) {
    console.error('transformValue: 数据转换失败', error, v)
    // 返回null表示转换失败，回到未选择状态
    return null
  }
}

/**
 * 通用有效期时间选择器组合式函数
 * @param props 组件属性
 * @param emits 组件事件
 */
export function useValidityTimePicker(
  props: ValidityTimePickerProps,
  emits: {
    (e: 'update:modelValue', value: ValidityTimePickerValue | null): void
    (e: 'change', value: ValidityTimePickerValue | null): void
  }
) {
  // 响应式状态
  const visible = ref(false)
  const datePickerVisible = ref(false)

  // 界面状态管理
  const showDateRangeSelection = ref(false) // 控制是否显示日期范围选择界面
  const currentDateType = ref<'start' | 'end'>('start') // 当前选择的日期类型

  // 错误状态管理
  const errorMessage = ref('')

  // 内部状态管理
  const value = ref<{
    qualValidityPeriod: number | null
    startTime?: string
    endTime?: string
  } | null>(transformValue(props.modelValue))

  // RadioGroup的选中状态管理
  const radioSelection = ref<boolean | null>(
    props.modelValue?.qualValidityPeriod === 0 ? true
      : props.modelValue?.qualValidityPeriod === 1 ? false
        : null
  )

  // 计算属性 - 永久有效状态（用于兼容性）
  const checked = computed({
    get() {
      return radioSelection.value
    },
    set(newVal: boolean | null) {
      radioSelection.value = newVal
    }
  })

  // 显示值计算
  const displayValue = computed(() => props.modelValue)

  // 格式化显示文本
  const formattedValue = computed(() => {
    const data = props.modelValue
    if (!data) return ''

    if (data.qualValidityPeriod === 0) { // Period.PERMANENT
      return '永久有效'
    }
    if (data.qualValidityPeriod === 1 && data.endTime) { // Period.NON_PERMANENT
      try {
        // 处理时间戳转换的辅助函数
        const formatTimestamp = (timestamp: string | number | undefined) => {
          if (!timestamp) return ''

          // 如果是字符串格式的时间戳，先转换为数字
          const numericTimestamp = typeof timestamp === 'string' ? Number(timestamp) : timestamp

          // 验证时间戳是否有效
          if (Number.isNaN(numericTimestamp) || numericTimestamp <= 0) {
            console.warn('formatTimestamp: 无效时间戳', timestamp)
            return ''
          }

          return dayjs(numericTimestamp).format('YYYY.M.D')
        }

        // 添加安全检查，确保startTime和endTime都是有效值
        const startTimeStr = formatTimestamp(data.startTime)
        const endTimeStr = formatTimestamp(data.endTime)

        // 确保格式化后的时间字符串有效
        if (!endTimeStr) return ''

        return startTimeStr ? `${startTimeStr}-${endTimeStr}` : `有效期至 ${endTimeStr}`
      } catch (error) {
        return ''
      }
    }
    return ''
  })

  // 验证有效性
  const isValid = computed(() => {
    if (!props.modelValue) return false

    if (props.modelValue.qualValidityPeriod === 0) return true // Period.PERMANENT

    return props.modelValue.qualValidityPeriod === 1 && !!props.modelValue.endTime // Period.NON_PERMANENT
  })

  // 工具方法
  const formatDate = (date: string | number | Date) => dayjs(date).format(props.dateFormat || 'YYYY-MM-DD')

  // 验证方法（仅基础校验，业务规则由表单层处理）
  const valid = (): boolean => {
    errorMessage.value = ''

    // 如果还未选择，提示用户选择
    if (!value.value) {
      errorMessage.value = '请选择有效期类型'
      return false
    }

    // 如果是永久有效，直接返回true
    if (value.value.qualValidityPeriod === 0) {
      return true
    }

    // 如果是期限有效，需要验证日期
    if (value.value.qualValidityPeriod === 1) {
      // 检查起始日期是否已选择
      if (!value.value.startTime) {
        errorMessage.value = '请选择起始日期'
        return false
      }

      // 检查结束日期是否已选择
      if (!value.value.endTime) {
        errorMessage.value = `请选择${props.endDateLabel || '结束日期'}`
        return false
      }

      // 验证结束日期必须晚于起始日期
      const startDate = dayjs(value.value.startTime).startOf('day')
      const endDate = dayjs(value.value.endTime).startOf('day')

      if (endDate.isSame(startDate) || endDate.isBefore(startDate)) {
        errorMessage.value = `${props.endDateLabel || '结束日期'}必须晚于起始日期`
        return false
      }

      return true
    }

    // 如果不是0也不是1，说明数据有问题
    errorMessage.value = '有效期类型无效'
    return false
  }

  // 清除错误信息
  const clearError = () => {
    errorMessage.value = ''
  }

  // 同步变更到外部（仅在确认时调用）
  const syncChange = () => {
    const v = value.value

    // 如果没有选择，发送null
    if (!v) {
      emits('update:modelValue', null)
      emits('change', null)
      return
    }

    try {
      const newValue: ValidityTimePickerValue = {
        qualValidityPeriod: v.qualValidityPeriod,
        startTime: v.startTime ? dayjs(v.startTime).valueOf() : (v.qualValidityPeriod === 1 ? 0 : undefined),
        endTime: v.endTime ? dayjs(v.endTime).valueOf() : undefined,
      }

      // 验证生成的时间戳是否有效
      if (newValue.startTime && Number.isNaN(Number(newValue.startTime))) {
        console.error('syncChange: startTime 时间戳无效', v.startTime)
        newValue.startTime = undefined
      }

      if (newValue.endTime && Number.isNaN(Number(newValue.endTime))) {
        console.error('syncChange: endTime 时间戳无效', v.endTime)
        newValue.endTime = undefined
      }

      emits('update:modelValue', newValue)
      emits('change', newValue)
    } catch (error) {
      console.error('syncChange: 同步数据时发生错误', error, v)
      // 如果同步失败，至少尝试发送一个基本的值
      emits('update:modelValue', null)
      emits('change', null)
    }
  }

  // 日期变更处理
  const changeDate = (date: string) => {
    // 添加参数验证
    if (!date || typeof date !== 'string') {
      console.error('changeDate: 无效的日期参数', date)
      return
    }

    // 确保有内部状态对象
    if (!value.value) {
      value.value = {
        qualValidityPeriod: 1, // Period.NON_PERMANENT
        startTime: '',
        endTime: '',
      }
    }

    if (value.value.qualValidityPeriod !== 1) {
      value.value.qualValidityPeriod = 1 // Period.NON_PERMANENT
    }

    value.value.endTime = date
  }

  const handleDisplayClick = () => {
    if (props.readOnly || props.disabled) return

    // 完全重置界面状态到初始状态（始终从第一层开始）
    showDateRangeSelection.value = false
    datePickerVisible.value = false
    currentDateType.value = 'start'
    errorMessage.value = ''

    // 同步RadioGroup状态
    radioSelection.value = props.modelValue?.qualValidityPeriod === 0 ? true
      : props.modelValue?.qualValidityPeriod === 1 ? false
        : null

    visible.value = true
  }

  const handleCancel = () => {
    // 关闭弹窗时不触发任何数据更新，重置所有状态到外部状态
    visible.value = false
    datePickerVisible.value = false
    showDateRangeSelection.value = false
    errorMessage.value = ''

    // 重要：重置内部状态到外部状态，避免状态不一致
    value.value = transformValue(props.modelValue)
    radioSelection.value = props.modelValue?.qualValidityPeriod === 0 ? true
      : props.modelValue?.qualValidityPeriod === 1 ? false
        : null
  }

  const handleConfirm = () => {
    // 确认时才同步数据到外部
    syncChange()
    visible.value = false
  }

  // 处理Sheets的confirm事件（右上角关闭按钮）
  const handleSheetsConfirm = () => {
    // 不管在哪个步骤，点击右上角关闭都应该是取消操作
    // 因为真正的确认操作有专门的确认按钮
    handleCancel()
  }

  const handleValidityTypeChange = (newVal: boolean) => {
    radioSelection.value = newVal

    if (newVal === true) {
      // 选择永久有效
      value.value = {
        qualValidityPeriod: 0, // Period.PERMANENT
        startTime: '',
        endTime: '',
      }
      // 立即同步数据并关闭弹层
      syncChange()
      visible.value = false
    } else if (newVal === false) {
      // 选择期限有效
      // 如果已有期限有效的数据，保留它；否则创建新的空状态
      if (!value.value || value.value.qualValidityPeriod !== 1) {
        value.value = {
          qualValidityPeriod: 1, // Period.NON_PERMANENT
          startTime: undefined,
          endTime: undefined,
        }
      }
      // 显示日期范围选择界面
      showDateRangeSelection.value = true
    }
    // newVal为null时（取消选择）不做任何操作
  }

  // 返回期限类型选择界面
  const backToValidityTypeSelection = () => {
    showDateRangeSelection.value = false
    // 如果用户没有完成日期选择就返回，应该重置为未选择状态
    if (value.value && value.value.qualValidityPeriod === 1 && !value.value.endTime) {
      value.value = transformValue(props.modelValue)
      radioSelection.value = props.modelValue?.qualValidityPeriod === 0 ? true
        : props.modelValue?.qualValidityPeriod === 1 ? false
          : null
    }
  }

  // 显示起始日期选择器
  const showStartDatePicker = () => {
    currentDateType.value = 'start'
    datePickerVisible.value = true
  }

  // 显示结束日期选择器
  const showEndDatePicker = () => {
    currentDateType.value = 'end'
    datePickerVisible.value = true
  }

  const showDatePicker = () => {
    currentDateType.value = 'end' // 兼容原有逻辑，默认选择结束日期
    datePickerVisible.value = true
  }

  const handleDateConfirm = (date: string | number | Date) => {
    // 添加严格的参数验证
    if (!date && date !== 0) {
      console.error('handleDateConfirm: date参数为空或未定义', date)
      datePickerVisible.value = false
      return
    }

    // 确保date是有效的日期值
    const dayjsInstance = dayjs(date)
    if (!dayjsInstance.isValid()) {
      console.error('handleDateConfirm: 无效的日期值', date)
      datePickerVisible.value = false
      return
    }

    const dateStr = dayjsInstance.format('YYYY-MM-DD')

    if (currentDateType.value === 'start') {
      // 设置起始日期
      // 确保有内部状态对象
      if (!value.value) {
        value.value = {
          qualValidityPeriod: 1, // Period.NON_PERMANENT
          startTime: undefined,
          endTime: undefined,
        }
      }
      if (value.value.qualValidityPeriod !== 1) {
        value.value.qualValidityPeriod = 1 // Period.NON_PERMANENT
      }
      value.value.startTime = dateStr
    } else {
      // 设置结束日期
      changeDate(dateStr)
    }

    // 清除错误信息
    clearError()

    datePickerVisible.value = false
    // 不在这里同步，等最终确认时再同步
  }

  // 日期范围确认处理
  const handleDateRangeConfirm = () => {
    if (valid()) {
      // 验证通过，同步数据并关闭弹窗
      syncChange()
      visible.value = false
    }
    // 验证失败时，错误信息已在valid方法中设置，不关闭弹窗
  }

  const handleDateCancel = () => {
    datePickerVisible.value = false
  }

  const reset = () => {
    value.value = null
    visible.value = false
    datePickerVisible.value = false
  }

  // 监听器
  watch(
    () => props.modelValue,
    () => {
      value.value = transformValue(props.modelValue)
      // 同步RadioGroup状态
      radioSelection.value = props.modelValue?.qualValidityPeriod === 0 ? true
        : props.modelValue?.qualValidityPeriod === 1 ? false
          : null
    }
  )

  return {
    // 状态
    visible,
    datePickerVisible,
    value,
    checked,
    radioSelection,
    errorMessage,

    // 界面状态
    showDateRangeSelection,
    currentDateType,

    // 计算属性
    displayValue,
    formattedValue,
    isValid,

    // 方法
    formatDate,
    changeDate,
    valid,
    clearError,
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleSheetsConfirm,
    handleValidityTypeChange,
    backToValidityTypeSelection,
    showDatePicker,
    showStartDatePicker,
    showEndDatePicker,
    handleDateConfirm,
    handleDateCancel,
    handleDateRangeConfirm,
    reset
  }
}

/**
 * 有效期时间选择器工具函数
 */
export const validityTimePickerUtils = {
  /**
   * 验证有效期时间选择器的值是否有效
   */
  isValidValue(value: ValidityTimePickerValue | null): boolean {
    if (!value) return false

    if (value.qualValidityPeriod === 0) return true // Period.PERMANENT

    return value.qualValidityPeriod === 1 && !!value.endTime // Period.NON_PERMANENT
  },

  /**
   * 格式化显示文本
   */
  formatDisplayText(value: ValidityTimePickerValue | null, dateFormat = 'YYYY-MM-DD'): string {
    if (!value) return ''

    if (value.qualValidityPeriod === 0) { // Period.PERMANENT
      return '永久有效'
    }
    if (value.qualValidityPeriod === 1 && value.endTime) { // Period.NON_PERMANENT
      try {
        // 处理时间戳转换，确保正确处理字符串格式的时间戳
        const numericTimestamp = typeof value.endTime === 'string' ? Number(value.endTime) : value.endTime

        // 验证时间戳是否有效
        if (Number.isNaN(numericTimestamp) || numericTimestamp <= 0) {
          console.warn('formatDisplayText: 无效时间戳', value.endTime)
          return ''
        }

        return `有效期至 ${dayjs(numericTimestamp).format(dateFormat)}`
      } catch (error) {
        console.error('formatDisplayText: 格式化失败', error, value.endTime)
        return ''
      }
    }

    return ''
  },

  /**
   * 创建永久有效的值
   */
  createPermanentValue(): ValidityTimePickerValue {
    return {
      qualValidityPeriod: 0, // Period.PERMANENT
      startTime: undefined,
      endTime: undefined
    }
  },

  /**
   * 创建期限有效的值
   */
  createLimitedValue(date: string | number | Date): ValidityTimePickerValue {
    try {
      // 处理时间戳转换，确保正确处理字符串格式的时间戳
      let processedDate = date
      if (typeof date === 'string' && /^\d+$/.test(date)) {
        // 如果是纯数字字符串，转换为数字时间戳
        processedDate = Number(date)
      }

      return {
        qualValidityPeriod: 1, // Period.NON_PERMANENT
        startTime: undefined,
        endTime: dayjs(processedDate).valueOf()
      }
    } catch (error) {
      console.error('createLimitedValue: 创建期限值失败', error, date)
      return {
        qualValidityPeriod: 1, // Period.NON_PERMANENT
        startTime: undefined,
        endTime: dayjs().valueOf()
      }
    }
  },

  /**
   * 检查日期是否已过期
   */
  isExpired(value: ValidityTimePickerValue | null): boolean {
    if (!value || value.qualValidityPeriod === 0) return false // Period.PERMANENT

    if (value.qualValidityPeriod === 1 && value.endTime) { // Period.NON_PERMANENT
      try {
        // 处理时间戳转换，确保正确处理字符串格式的时间戳
        const numericTimestamp = typeof value.endTime === 'string' ? Number(value.endTime) : value.endTime

        // 验证时间戳是否有效
        if (Number.isNaN(numericTimestamp) || numericTimestamp <= 0) {
          console.warn('isExpired: 无效时间戳', value.endTime)
          return false
        }

        return dayjs().isAfter(dayjs(numericTimestamp))
      } catch (error) {
        console.error('isExpired: 检查过期失败', error, value.endTime)
        return false
      }
    }

    return false
  },

  /**
   * 检查是否有有效的有效期设置
   */
  hasValid(value?: ValidityTimePickerValue): boolean {
    const period = value?.qualValidityPeriod
    if (period === 0) return true // Period.PERMANENT
    return period === 1 && !!value?.endTime // Period.NON_PERMANENT
  }
}
