/**
 * 菜单管理相关API服务
 */

import { http } from '@xhs/launcher'
import type {
  GetMenuUploadStatusParams,
  GetMenuUploadStatusResponse,
  GetMenuListParams,
  GetMenuListResponse,
  CreateMenuParams,
  CreateMenuResponse,
  MenuUploadStatus,
  MenuStatus
} from '../types/menu'

/**
 * 获取菜单上传状态
 * 判断商家是否已上传菜单，包括历史上传记录和提交状态
 */
export function getMenuUploadStatus(
  params: GetMenuUploadStatusParams = {},
  options = {}
): Promise<MenuUploadStatus> {
  return http.get<GetMenuUploadStatusResponse>(
    'GET_MENU_UPLOAD_STATUS',
    { params, transform: false, ...options }
  ).then(response => response.data)
}

/**
 * 获取菜单列表
 */
export function getMenuList(
  params: GetMenuListParams = {},
  options = {}
): Promise<GetMenuListResponse['data']> {
  return http.get<GetMenuListResponse>(
    'GET_MENU_LIST',
    { params, transform: false, ...options }
  ).then(response => response.data)
}

/**
 * 创建菜单
 */
export function createMenu(
  params: CreateMenuParams,
  options = {}
): Promise<CreateMenuResponse['data']> {
  return http.post<CreateMenuResponse>(
    'CREATE_MENU',
    params,
    { transform: false, ...options }
  ).then(response => response.data)
}

/**
 * Mock 数据 - 用于开发测试
 * 实际项目中应该从后端API获取真实数据
 */
export const mockMenuUploadStatus = (): Promise<MenuUploadStatus> => {
  // 模拟API延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      // 这里可以根据需要返回不同的状态进行测试
      const mockStatus: MenuUploadStatus = {
        hasEverUploaded: false, // 改为 true 可以测试已上传菜单的情况
        hasSubmittedMenu: false,
        currentStatus: MenuStatus.NOT_CREATED,
        latestMenu: undefined
      }
      resolve(mockStatus)
    }, 500)
  })
}

/**
 * 检查商家菜单状态的工具函数
 */
export class MenuStatusChecker {
  /**
   * 判断是否应该显示引导面板
   * 对于从未上传菜单的商家（包括历史上传过但未提交的）显示引导面板
   */
  static shouldShowGuideSheet(status: MenuUploadStatus): boolean {
    // 从未上传过菜单，或者上传过但从未提交过
    return !status.hasEverUploaded || !status.hasSubmittedMenu
  }

  /**
   * 判断是否应该跳转到菜单管理列表
   * 对于已上传菜单的商家跳转到管理列表
   */
  static shouldNavigateToList(status: MenuUploadStatus): boolean {
    // 已经上传过菜单且至少提交过一次
    return status.hasEverUploaded && status.hasSubmittedMenu
  }

  /**
   * 获取菜单状态描述文本
   */
  static getStatusText(status: MenuStatus): string {
    const statusTextMap = {
      [MenuStatus.NOT_CREATED]: '未创建',
      [MenuStatus.DRAFT]: '草稿',
      [MenuStatus.SUBMITTED]: '已提交',
      [MenuStatus.REVIEWING]: '审核中',
      [MenuStatus.APPROVED]: '审核通过',
      [MenuStatus.REJECTED]: '审核拒绝'
    }
    return statusTextMap[status] || '未知状态'
  }

  /**
   * 判断菜单是否可以编辑
   */
  static canEditMenu(status: MenuStatus): boolean {
    return [MenuStatus.NOT_CREATED, MenuStatus.DRAFT, MenuStatus.REJECTED].includes(status)
  }

  /**
   * 判断菜单是否可以提交
   */
  static canSubmitMenu(status: MenuStatus): boolean {
    return [MenuStatus.DRAFT, MenuStatus.REJECTED].includes(status)
  }
}

/**
 * 菜单管理相关的常量配置
 */
export const MENU_CONFIG = {
  // 引导面板配置
  GUIDE_SHEET: {
    title: '菜单管理',
    description: '您可以拍摄店内实体菜单，平台会自动为您识别生成菜单列表。提交后平台将会为你聚合用户的菜品评价，门店将更容易被搜索到。',
    exampleImageUrl: 'https://picasso-static.xiaohongshu.com/fe-platform/menu-example.png', // 需要替换为实际的示例图片URL
    actions: {
      photoText: '拍照自动生成菜单',
      manualText: '手动输入菜单'
    }
  },
  
  // 路由配置
  ROUTES: {
    PHOTO_GENERATE: '/lifesellerh5/menu/photo-generate',
    MANUAL_INPUT: '/lifesellerh5/menu/manual-input',
    MENU_LIST: '/lifesellerh5/menu/list'
  }
}
