{"name": "lifesellerh5", "version": "1.2.14", "description": "", "scripts": {"dev": "formula dev -p 8000", "preinstall": "[ -d ./.hooks ] && (git config --add core.hooksPath ./.hooks) || ([ -d ./.git ] && formula githooks || echo 'Please run git init')", "devtools": "modular start ./devtools.config.js"}, "author": "luzhongkuan <<EMAIL>>", "license": "ISC", "private": true, "dependencies": {"@xhs/cube-style": "latest", "@xhs/delight-charts-mobile": "^1.0.17", "@xhs/imagepreviewh5": "0.0.5", "@xhs/launcher-plugin-eaglet": "5.6.10", "@xhs/onix-icon": "^10.0.0", "@xhs/ozone-share": "1.6.1-0", "@xhs/protobuf-pages-lifeseller-tracker": "0.7.1", "@xhs/reder-icon-svg-ReDs_icon": "^1.0.8", "@xhs/reds-h5": "1.1.0", "@xhs/rex-uploader": "2.0.4", "@xhs/riant": "^1.1.2", "@xhs/rim-router": "^2.0.3", "@xhs/water-kaipingyidongBduan": "^1.0.0", "@xhs/water-reds-dark": "^1.4.0", "@xhs/water-reds-light": "^1.4.0", "@xhs/water-theme-professional-account-platform": "^1.0.0", "swiper": "^11.0.5"}, "devDependencies": {"@xhs/formula-plugin-swc": "latest"}}