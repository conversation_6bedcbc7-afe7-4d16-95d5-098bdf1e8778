<template>
  <Space
    direction="vertical"
    style="height: 100%; width: 100%;"
  >
    <Space
      class="ultra-list-container"
    >
      <div class="custom-tabs">
        <div
          v-for="tab in tabs"
          :key="tab.value"
          :class="['custom-tab', { active: params.styleType === tab.value }]"
          @click="() => {
            params.styleType = tab.value
            if(tab.value === 2) stashCopytextId.value = params.copytextId
            params.copytextId = tab.value === 1 ? stashCopytextId.value : [0]
          }"
        >
          {{ tab.label }}
        </div>
      </div>

      <Space>
        <Select
          v-if="params.styleType === 1"
          v-model="params.copytextId"
          prefix="文案"
          :multiple="true"
          placeholder="请选择文案"
          :options="config?.copyTextList?.map(item => ({
            label: item.content,
            value: item.id,
          }))"
        />
        <ChooseShopButton
          v-model="params.shopIds"
          :binded-num="params.shopIds?.length ?? 0"
          :poi-group-id="''"
          :poi-group-name="''"
          :business-enum="BusinessEnum.OFFLINE_SCAN"
        />
      </Space>

      <!-- 门店物料卡片区 -->
      <div class="material-cards">
        <div v-for="item in styleList" :key="item?.id" class="material-card">
          <div class="card-header">
            <Checkbox v-model:checked="item.checked" />
            <span style="margin-left: 8px;">{{ item.name }} <span style="color: #999;" v-if="item.sizeRatio">{{ item.sizeRatio }}</span></span>
          </div>
          <div class="card-body">
            <img :src="item?.exampleImage" class="qr-img" />
          </div>
        </div>
      </div>

    </Space>
    <Button
      type="primary"
      class="generate-btn"
      @click="handleGenerate"
    >
      生成
    </Button>
  </Space>
</template>

<script setup lang="tsx">
  import { onMounted, ref, computed } from 'vue'
  import {
    Space, Select, Button, Checkbox, toast2 as toast, Text, Link
  } from '@xhs/delight'
  import ChooseShopButton from 'shared/seller/components/ChooseShopModal/ChooseShopButton.vue'
  import { tryCatch } from 'shared/utils/tryCatch'
  import { BusinessEnum } from 'shared/seller/components/ChooseShopModal/type'
  import { getMaterialConfig, ISizeTypeList } from '~/services/edith_get_material_config'
  import { postMaterialGenerate, IPostMaterialGeneratePayload } from '~/services/edith_post_material_generate'

  const stashCopytextId = ref<number[]>([])

  const tabs = [
    { label: '设计样式', value: 1 },
    { label: '基础样式', value: 2 },
  ]

  const params = ref<{
    shopIds: string[]
    styleType: number
    shopStyleList: ISizeTypeList[]
    copytextId: number[]
  }>({
    shopIds: [],
    styleType: 1,
    shopStyleList: [],
    copytextId: [],
  })

  const config = ref<{
    copyTextList: {
      id: number
      content: string
    }[]
    sizeTypeList: {
      id: number
      name: string
      checked?: boolean
    }[]
    defaultTypeList: {
      id: number
      name: string
      exampleImage?: string
      checked?: boolean
    }[]
  }>()

  const styleList = computed(() => (params.value.styleType === 1 ? config.value?.sizeTypeList : config.value?.defaultTypeList))

  const fetchData = async () => {
    const res = await getMaterialConfig({})
    const { basicExampleImage } = res || {}
    const defaultTypeList = [
      {
        id: 0,
        name: '二维码',
        exampleImage: basicExampleImage,
      }
    ]
    config.value = {
      ...res,
      defaultTypeList
    }
  }

  const handleGenerate = async () => {
    const { shopIds, styleType, copytextId } = params.value
    const templateId = styleList.value?.reduce((acc, item) => {
      if (item.checked) acc.push(item.id)
      return acc
    }, [] as string[])

    if (!templateId?.length) {
      return toast.warning('请选择样式模板')
    }
    if (!shopIds.length) {
      return toast.warning('请选择门店')
    }
    if (!copytextId?.length && styleType === 1) {
      return toast.warning('请选择文案')
    }

    const payload: IPostMaterialGeneratePayload = {
      styleType,
      templateId,
      shopIds,
      copytextId,
    }
    const [, err] = await tryCatch(postMaterialGenerate, payload)
    if (err) {
      return toast.danger(err.msg || '生成失败')
    }
    toast.success({
      content: <Text color="text-title">生成任务创建成功，请前往 <Link href="/app-activity/materials/download">物料列表</Link> 查看和下载</Text>,
      strong: true
    })
  }

  onMounted(() => {
    fetchData()
  })
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  height 100%
  background #fff
  border-radius 8px
  padding 24px
  flex-direction column
  align-items flex-start
  justify-content flex-start
  gap 16px

.custom-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  width: fit-content;
  padding: 4px;
}
.custom-tab {
  padding: 6px 24px;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
  background: transparent;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.custom-tab.active {
  background: #fff;
  color: #222;
  font-weight: 500;
}
.material-cards {
  display: flex;
  gap: 24px;
}
.material-card {
  background: #fff;
  border: 1px solid #e5e6eb;
  width: 260px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
}
.card-header {
  font-size: 16px;
  color: #222;
  padding: 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e6eb;
}
.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
}
.qr-card {
  background: #ff2d4a;
  border-radius: 24px;
  padding: 20px 12px 12px 12px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qr-title-row {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 2px;
}
.qr-title {
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  flex: 1;
}
.qr-badge {
  background: #fff;
  color: #ff2d4a;
  font-size: 12px;
  border-radius: 8px;
  padding: 2px 8px;
  margin-left: 8px;
  font-weight: bold;
}
.qr-subtitle {
  color: #fff;
  font-size: 14px;
  margin-bottom: 12px;
  width: 100%;
}
.qr-img {
  width: 100%;
  background: #fff;
  border-radius: 16px;
}
.qr-footer {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 13px;
  width: 100%;
  margin-top: 8px;
}
.qr-loc-icon {
  margin-right: 4px;
}
.qr-shop {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.generate-btn {
  margin-top: 24px;
  width: 88px;
  align-self: center;
}
</style>
