import { postBusinessdataGet } from '~/services/edith_post_businessdata_get'
import { getRoadSignActivityPage, IGetRoadSignActivityPagePayload } from '~/services/edith_get_road_sign_activity_page'
import { postMaterialListQuery } from '~/services/edith_post_material_list_query'
import { DataBlockKeyEnum } from './data/type'

export const getActivityList = async (params: IGetRoadSignActivityPagePayload) => {
  const p = {
    ...params,
    // @ts-ignore
    pageNo: params.pageNum || 1,
    pageSize: params.pageSize || 10,
  }
  // @ts-ignore
  if (p.pageNum) delete p.pageNum
  const res = await getRoadSignActivityPage(p, { needToast: false })
  return {
    listSource: res.activityDTOList ?? [],
    total: res.total ?? 0,
  }
}

export const getMaterialList = async (params: any) => {
  const {
    groupIds, sizes, styles, shopIds, pageNum = 1, pageSize = 10,
    materialId
  } = params
  const p: any = {
    pageNo: pageNum,
    pageSize,
  }
  if (materialId) {
    p.materialId = materialId
  }
  if (groupIds?.length) {
    p.groupIds = groupIds
  }
  if (sizes?.length) {
    p.sizes = sizes
  }
  if (styles?.length) {
    p.styles = styles
  }
  if (shopIds?.length) {
    p.shopIds = shopIds
  }
  // @ts-ignore
  if (p.pageNum) delete p.pageNum
  const res = await postMaterialListQuery(p, { needToast: false })
  return {
    listSource: res.materialList ?? [],
    total: res.total ?? 0,
    extra: {
      isGenerating: res.isGenerating ?? false,
    },
  }
}

export const getActivityNoteList = async (params: {
  pageNum: number
  pageSize: number
  activityId: string
  orderBy: { field: string; orderBy: string }[]
}) => {
  const res = await postBusinessdataGet({
    requestBody: {
      blockElements: [
        {
          blockKey: DataBlockKeyEnum.noteDetailList,
          filterMap: {
            activityId: params.activityId as string,
            dateType: '1',
          },
          orderBy: params.orderBy?.[0]?.orderBy ? params.orderBy : [],
          page: params.pageNum,
          size: params.pageSize,
        },
      ]
    }
  })
  const noteRes = JSON.parse(res?.responseJson || '[]')?.find(item => item.blockKey === DataBlockKeyEnum.noteDetailList)
  if (noteRes) {
    return {
      listSource: noteRes?.data?.map(item => ({
        note_url: item.note_url?.value,
        like_num: item.like_num?.value,
        note_title: item.note_title?.value,
        note_publish_time: item.note_publish_time?.value,
        fav_num: item.fav_num?.value,
        cmt_num: item.cmt_num?.value,
      })),
      total: noteRes?.count ?? 0,
    }
  }
}
