import { Text } from '@xhs/delight'
import TableColumn from 'shared/components/TableColumn/index.vue'
import ChooseShopFilter from './chooseShopFilter.vue'
import PreviewColumn from './previewColumn.vue'

const styleMapping = {
  1: '设计样式',
  2: '基础样式',
}

export const getFilterScheme = (config: any) => {
  const { groupList = [], sizeList = [], styleList = [] } = config
  return [
    {
      name: 'materialId',
      label: '物料ID',
      component: {
        is: 'Input',
        props: {
          placeholder: '请输入物料ID',
        },
      },
      defaultValue: '',
    },
    {
      name: 'styles',
      label: '样式类型',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择样式类型',
          multiple: true,
          options: styleList?.map(item => ({ label: styleMapping[item], value: item })) || []
        },
      },
      defaultValue: [],
    },
    {
      name: 'sizes',
      label: '尺寸',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择尺寸',
          multiple: true,
          options: sizeList?.map(item => ({ label: item, value: item })) || []
        },
      },
      defaultValue: [],
    },
    {
      name: 'groupIds',
      label: '所属区域',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择所属区域',
          multiple: true,
          clearable: true,
          options: groupList?.map(item => ({ label: item.groupName, value: item.groupId })) || []
        },
      },
      defaultValue: [],
    },
    {
      name: 'shopIds',
      label: '',
      component: {
        is: ChooseShopFilter,
      },
      defaultValue: [],
    },
  ]
}

// filterScheme: useFilterScheme(),

export const tableScheme = {
  filterScheme: getFilterScheme({}),
  tableColumnScheme: [
    {
      title: '物料ID',
      minWidth: 60,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="materialId" />
    },
    {
      title: '码类型',
      minWidth: 100,
      render: () => <Text>门店二维码</Text>
    },
    {
      title: '样式类型',
      width: 100,
      render: ({ rowData }) => <Text>{styleMapping[rowData.style as keyof typeof styleMapping]}</Text>
    },
    {
      title: '尺寸',
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="size" />
    },
    {
      title: '文案',
      minWidth: 60,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="text" />
    },
    {
      title: '预览图',
      width: 120,
      render: ({ rowData }) => <PreviewColumn rowData={rowData}/>
    },
    {
      title: '所属门店',
      width: 100,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="shopName" />
    },
    {
      title: '所属区域',
      width: 100,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="group" />
    },
  ],
  tableActionScheme: [
    {
      text: '重新生成',
      visible: rowData => rowData.status === 3,
    },
    {
      text: '下载',
      visible: rowData => rowData.status === 2 && rowData.imageUrl,
      disabled: rowData => [0, 1].includes(rowData.status) || !rowData.imageUrl,
    },
    // {
    //   text: '操作日志',
    //   disabled: rowData => [
    //     ActivityStatusEnum.ENDED,
    //     ActivityStatusEnum.TERMINATED,
    //   ].includes(rowData.activityState),
    // },
  ],
}
