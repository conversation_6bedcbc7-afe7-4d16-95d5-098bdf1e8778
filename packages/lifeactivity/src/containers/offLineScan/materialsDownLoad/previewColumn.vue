<template>
  <div>
    <Text v-if="[0, 1].includes(rowData.status)" class="preview-loading">
      <Spinner :spinning="loading" />&nbsp;生成中
    </Text>
    <Text v-else-if="rowData.status === 3" class="preview-fail">
      <Icon :icon="Info" fill="#FB3367" />生成失败
    </Text>
    <Img v-else-if="rowData.status === 2 && rowData.imageUrl"
         class="preview-image"
         :src="rowData.imageUrl || defaultImage"
         @click="handlePreview()"
    />
    <Modal
      v-model:visible="visible"
      title=""
      :with-footer="false"
      :content-style="{ padding: '0' }"
      @ok="handlePreview"
    >
      <Img style="width: 100%; height: 100%;" :src="rowData.imageUrl" />
    </Modal>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Text, Spinner, Icon, Modal
  } from '@xhs/delight'
  import { Info } from '@xhs/delight/icons'

  const loading = ref(true)
  const visible = ref(false)
  const defaultImage = 'https://fe-platform.xhscdn.com/platform/104101l031ii7gtm10806d7dvsct00000000000vudv9ag'

  defineProps<{
    rowData: {
      /** 状态 - 0-待处理,1-处理中,2-已完成,3-失败 */
      status: 0 | 1 | 2 | 3
      imageUrl: string
    }
  }>()

  const handlePreview = () => {
    visible.value = true
  }

</script>
<style lang="stylus" scoped>
  .preview-loading
    font-size 14px
    color rgba(0, 0, 0, 0.85)

  .preview-fail
    font-size 14px
    color #FB3367
    display flex
    align-items center
    gap 4px

  .preview-image
    width 42px
    object-fit cover
    border-radius 4px
</style>
