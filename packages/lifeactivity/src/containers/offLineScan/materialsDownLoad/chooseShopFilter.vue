<template>
  <ChooseShopButton
    v-model="value"
    :binded-num="value?.length ?? 0"
    :poi-group-id="''"
    :poi-group-name="''"
    :business-enum="BusinessEnum.OFFLINE_SCAN"
  />
</template>
<script setup lang="ts">
  import { computed } from 'vue'
  import ChooseShopButton from 'shared/seller/components/ChooseShopModal/ChooseShopButton.vue'
  import { BusinessEnum } from 'shared/seller/components/ChooseShopModal/type'

  const props = defineProps<{
    modelValue: string[]
  }>()

  const emit = defineEmits<{(e: 'update:modelValue', value: string[]): void
  }>()

  const value = computed({
    get: () => props.modelValue || [],
    set: value => emit('update:modelValue', value),
  })

</script>
