<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParams"
        :config="filterConfig"
      ></OutlineFilter>
    </Space>

    <div class="ultra-list-body">
      <Space
        v-loading="actionLoading"
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Space>
          <Button :disabled="!rowSelected.length" style="width: 88px" type="primary" @click="toBatchDownLoad(rowSelected)">批量下载</Button>
          <Text v-if="rowSelected.length">已选{{ rowSelected.length }}个  物料数量：{{ total }}</Text>
        </Space>
        <Table
          v-model:selected="rowSelected"
          size="large"
          row-key="materialId"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
          :row-selection="rowSelection"
          @selectedChange="changeSelected"
        >
        </Table>
        <Pagination
          v-if="total > 10"
          v-model="baseParams.pageNum"
          v-model:pageSize="baseParams.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
  </Space>

</template>
<script setup lang="ts">
  import {
    ref, watch, onMounted, onBeforeUnmount
  } from 'vue'
  import {
    Space, Table2 as Table, Pagination, vLoading, Button, toast2 as toast, Text
  } from '@xhs/delight'
  import { tryCatch } from 'shared/utils/tryCatch'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import { useList } from 'shared/composables/use-list'
  import { useTableColumns, useFilter, spaceProps } from 'shared/composables/use-props'
  import { tableScheme, getFilterScheme } from './scheme'
  import { getMaterialList } from '../services'
  import { getMaterialListConfig } from '~/services/edith_get_material_list_config'
  import { postMaterialRegenerate } from '~/services/edith_post_material_regenerate'
  import { postMaterialDownload } from '~/services/edith_post_material_download'
  // import { getDetailV2 } from '~/services/edith_get_detail_v2'

  const scheme = ref<{
    tableColumnScheme: any[]
    filterScheme?: any[]
    tableActionScheme?: any[]
  }>(tableScheme)
  const actionLoading = ref(false)

  const auditModalVisible = ref(false)
  const rejectReason = ref('')
  watch(() => auditModalVisible.value, () => {
    rejectReason.value = ''
  })

  const rowSelection = {
    getCheckboxProps: v => ({
      checked: false,
      disabled: v.status !== 2 || !v.imageUrl,
      selectable: true,
    }),
  }

  const rowSelected = ref<string[]>([])
  const selectItems = ref<any[]>([])
  const changeSelected = (_: any, v) => {
    selectItems.value = v
  }

  /**
   * 下载图片，文件名自动从URL中提取
   * @param {string} url 图片地址
   */
  async function downloadImage(url) {
    let filename = 'download.png'
    try {
      // 匹配最后一个 / 后的文件名部分
      const match = decodeURIComponent(url).match(/\/([^/]+)$/)
      if (match) {
        filename = match[1]
      }
    } catch (e) {
      filename = 'download.png'
    }

    try {
      const response = await fetch(url, { mode: 'cors' })
      if (!response.ok) throw new Error('网络错误')
      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = blobUrl
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)

      URL.revokeObjectURL(blobUrl)
    } catch (e) {
      window.open(url, '_blank')
    }
  }

  const toBatchDownLoad = async materialIds => {
    const len = materialIds.length
    if (len > 1000) {
      return toast.danger(`最多选择1000个物料，当前选择${len}个`)
    }
    const [res, err] = await tryCatch(postMaterialDownload, {
      materialIds,
    })
    if (err) {
      return toast.danger(err.msg || '下载失败')
    }
    if (res?.fileUrl) {
      if (len === 1) {
        await downloadImage(res.fileUrl)
        toast.success('物料下载成功')
      } else {
        window.open(res.fileUrl, '_blank')
        toast.success('批量下载成功')
      }
    }
    // try {
    //   // const res = await http.post('http://castle.xiaohongshu.com/api/edith/long_task/task/submit/v2', {
    //   //   taskName: 'batch_download_material',
    //   //   input: {
    //   //     extra: JSON.stringify({
    //   //       materialIds: rowSelected.value.join(','),
    //   //     }),
    //   //   },
    //   //   subsystemAlias: 'ark',
    //   // }, {
    //   //   headers: {
    //   //     authorization: 'AT-d9c19e982eea405ab7560a5811bd4c0c-2711f594a07e4157bcf35319c9754f35x-subsystem: ark',
    //   //     subsystem: 'ark',
    //   //     'x-subsystem': 'ark',
    //   //     'x-subsystem-alias': 'ark',
    //   //   },
    //   // })
    // } catch (error) {

    // }
    // // 任务注册
    // const [taskRes, err] = await tryCatch(postSubmitV2, {
    //   taskName: 'batch_download_material',
    //   input: {
    //     extra: JSON.stringify({
    //       materialIds: rowSelected.value.join(','),
    //     }),
    //     fileUrl: '',
    //   },
    //   subsystemAlias: 'ark',
    // })
    // if (err) {
    //   return toast.danger(err.msg || '批量下载失败')
    // }
    // const { taskId } = taskRes as any
    // pollTaskStatus(taskId)
    // router.push('/app-activity/offline-scan/batch-down-load')
  }

  const fetchData = async () => {
    const res = await getMaterialListConfig({})
    const filterScheme = getFilterScheme(res)
    scheme.value.filterScheme = filterScheme
  }

  onMounted(() => {
    fetchData()
  })

  const {
    listSource,
    total,
    loading,
    baseParams,
    filterParams,
    extra,
    fetchList
  } = useList(scheme, getMaterialList)

  const fetchTimeout = ref<any>(null)

  watch(
    () => extra.value.isGenerating,
    isGenerating => {
      // 先清理旧的 timeout
      if (fetchTimeout.value) {
        clearTimeout(fetchTimeout.value)
        fetchTimeout.value = null
      }
      if (isGenerating) {
        const poll = async () => {
          await fetchList(false)
          if (extra.value.isGenerating) {
            fetchTimeout.value = setTimeout(poll, 5000)
          }
        }
        // 首次不立即执行，延迟后再执行
        fetchTimeout.value = setTimeout(poll, 5000)
      }
    },
    { immediate: true }
  )

  onBeforeUnmount(() => {
    if (fetchTimeout.value) {
      clearTimeout(fetchTimeout.value)
      fetchTimeout.value = null
    }
  })

  const { tableColumns } = useTableColumns(scheme, [
    rowData => handleRegenerate(rowData),
    rowData => toBatchDownLoad([rowData.materialId]),
  ])
  const { filterConfig } = useFilter(scheme, fetchList)

  fetchList()

  const handleRegenerate = async (rowData: any) => {
    const payload = {
      materialId: rowData.materialId,
    }
    await tryCatch(postMaterialRegenerate, payload)
    toast.success('重新生成成功')
    fetchList()
  }

</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
  .ultra-list-body
    width 100%
  .ultra-list-content
    width 100%
    background #FFFFFF
    border-radius 8px
    padding 24px
  :deep(.ultra-material-toolbar-wrap)
    padding 0
  :deep(.d-pagination)
    width 100%
</style>
