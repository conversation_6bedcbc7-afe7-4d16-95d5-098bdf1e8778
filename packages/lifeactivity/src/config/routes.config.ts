import { RouteRecordRaw } from 'vue-router'
import { platform } from 'shared/config/platform.config'

import AppLayout from '~/Layout/AppLayout.vue'

const NeedLoginMeta = {
  auth: {
    check: ['isLogin'],
    fallback: 'Anonymous',
  },
}

const routes = [
  {
    name: 'lifeAppActivityHome',
    path: '/app-activity/home',
    component: () => import('../containers/home/<USER>'),
    meta: {
      title: '通兑活动',
      showSideMenu: true,
      pageKey: 'app_activity_list_home',
      activeMenu: 'ActivityHome',
      attributes: {
        app_platform: platform
      }
    },
  },
  {
    name: 'lifeAppActivityFormDetail',
    path: '/app-activity/form-detail/:id',
    component: () => import('../containers/formDetail/index.vue'),
    meta: {
      title: '报名活动',
      showSideMenu: true,
      pageKey: 'app_activity_form_detail',
      activeMenu: 'ActivityHome',
      attributes: {
        app_platform: platform
      }
    },
  },
  {
    name: 'lifeAppActivitySendFree',
    path: '/app-activity/send-free',
    redirect: '/app-activity/send-free/list',
    children: [
      {
        name: 'lifeAppActivitySendFreeList',
        path: '/app-activity/send-free/list',
        component: () => import('../containers/sendFree/index.vue'),
        meta: {
          title: '免费送活动',
          showSideMenu: true,
          pageKey: 'app_activity_send_free_list',
          activeMenu: 'HomeFree',
        },
      },
      {
        name: 'lifeAppActivitySendFreeDetail',
        path: '/app-activity/send-free/detail/:id?',
        component: () => import('../containers/sendFree/detail/index.vue'),
        meta: {
          title: '活动详情',
          showSideMenu: false,
          pageKey: 'app_activity_send_free_detail',
          activeMenu: 'HomeFree',
        },
      },
    ]
  },
  {
    name: 'lifeAppActivityMaterials',
    path: '/app-activity/materials',
    redirect: '/app-activity/materials/qr',
    children: [
      {
        name: 'lifeAppActivityMaterials',
        path: '/app-activity/materials/qr',
        component: () => import('../containers/offLineScan/materials/index.vue'),
        meta: {
          title: '活动物料',
          showSideMenu: true,
          activeMenu: 'Materials',
          pageKey: 'activity_materials_qr',
        },
      },
      {
        name: 'lifeAppActivityMaterialsDownload',
        path: '/app-activity/materials/download',
        component: () => import('../containers/offLineScan/materialsDownLoad/index.vue'),
        meta: {
          title: '活动物料',
          showSideMenu: true,
          activeMenu: 'Materials',
          pageKey: 'activity_materials_download',
        },
      },
    ]
  },
  {
    name: 'lifeAppActivityOfflineScan',
    path: '/app-activity/offline-scan',
    redirect: '/app-activity/offline-scan/list',
    children: [
      {
        name: 'lifeAppActivityOfflineScanList',
        path: '/app-activity/offline-scan/list',
        component: () => import('../containers/offLineScan/list/index.vue'),
        meta: {
          title: '线下扫码',
          showSideMenu: true,
          activeMenu: 'OfflineScan',
          pageKey: 'activity_offline_scan_list',
        },
      },
      {
        name: 'lifeAppActivityOfflineScanDetail',
        path: '/app-activity/offline-scan/detail/:id?',
        component: () => import('../containers/offLineScan/detail/index.vue'),
        meta: {
          title: '活动详情',
          showSideMenu: true,
          activeMenu: 'OfflineScan',
          pageKey: 'activity_offline_scan_detail',
        },
      },
      {
        name: 'lifeAppActivityOfflineScanNote',
        path: '/app-activity/offline-scan/note/:id?',
        component: () => import('../containers/offLineScan/note/index.vue'),
        meta: {
          title: '活动笔记',
          showSideMenu: true,
          activeMenu: 'OfflineScan',
          pageKey: 'activity_offline_scan_note',
        },
      },
      {
        name: 'lifeAppActivityOfflineScanCreate',
        path: '/app-activity/offline-scan/create/:id?',
        component: () => import('../containers/offLineScan/create/index.vue'),
        meta: {
          title: '线下扫码',
          showSideMenu: true,
          activeMenu: 'OfflineScan',
          pageKey: 'activity_offline_scan_form',
        },
      },
      {
        name: 'lifeAppActivityOfflineScanData',
        path: '/app-activity/offline-scan/data/:id?',
        component: () => import('../containers/offLineScan/data/index.vue'),
        meta: {
          title: '活动数据',
          showSideMenu: true,
          activeMenu: 'OfflineScan',
          pageKey: 'activity_offline_scan_data',
        },
      },
    ]
  }
]

const rootRoutes:RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/app-activity/home',
  },
  {
    name: 'Anonymous',
    path: '/app-activity/anonymous',
    component: () => 'anonymous',
  },
  {
    path: '/app-activity/',
    component: AppLayout,
    meta: NeedLoginMeta,
    redirect: '/app-activity/home',
    children: routes
  }
]

export default rootRoutes
