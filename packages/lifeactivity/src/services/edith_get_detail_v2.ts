/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 15691
  * @name: 长任务-查询任务v2
  * @identifier: api.edith.long_task.task.detail.v2.get
  * @version: undefined
  * @path: /api/edith/long_task/task/detail/v2
  * @method: get
  * @description: 查询状态
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetDetailV2Payload {
  /** 1 */
  taskId: string
}


export function getDetailV2(params: IGetDetailV2Payload, options = {}): Promise<void> {
  return http.get('/api/edith/long_task/task/detail/v2', { params, transform: true, ...{ "needToast": true }, ...options })
}
