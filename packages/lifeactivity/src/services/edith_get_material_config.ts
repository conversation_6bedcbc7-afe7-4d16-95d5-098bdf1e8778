/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53056
  * @name: 物料中心-文案及尺寸配置
  * @identifier: api.edith.material_center.material.config.get
  * @version: undefined
  * @path: /api/edith/material_center/material/config
  * @method: get
  * @description: 读取apollo文案及尺寸配置
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IResponse {
	/** // */
	success: boolean
	/** // */
	msg?: string
	/** // */
	code?: number
	/** // */
	data?: string
	/** // */
	bizCode?: string
}

export interface ISizeTypeList {
	/** 尺寸类型ID */
	id: number
	/** // */
	name: string
	/** 例图 */
	exampleImage: string
	/** 尺寸（如：A4、A5） */
	exampleImageName?: string
	/** 尺寸示例图片URL */
	sizeRatio?: string
}

export interface ICopyTextList {
	/** 文案ID */
	id: number
	/** // */
	content: string
}

export interface IData {
	/** // */
	response: IResponse
	/** // */
	sizeTypeList: ISizeTypeList[]
	/** 尺寸类型列表 */
	copyTextList: ICopyTextList[]
	/** 基础样式图片 */
	basicExampleImage?: string
}

export interface IGetMaterialConfigResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMaterialConfig(options = {}): Promise<IData> {
  return http.get('/api/edith/material_center/material/config', { transform: true, ...{"needToast":true}, ...options })
}
