/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 24630
  * @name: 本地生活-门店管理列表页查询
  * @identifier: api.edith.life_service.poi.poi_claim_info.page.get
  * @version: undefined
  * @path: /api/edith/life_service/poi/poi_claim_info/page
  * @method: get
  * @description: 门店管理列表页查询
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetPoiClaimInfoPagePayload {
	/** 当前页 */
	pageNo: number
	/** 页大小 */
	pageSize: number
	/** poi信息 */
	poiInfo?: string
	/** 认领状态 */
	claimStatus?: string
	/** 审核状态 */
	auditStatus?: string
	/** 商家id */
	sellerId?: string
	/** ids */
	ids?: string
	/** 是否过滤待修改 */
	filterNeedUpdateQualification?: boolean
	/** 登录用户 */
	userId?: string
	/** 省 */
	provinceName?: string
	/** 市 */
	cityName?: string
	/** 区 */
	districtName?: string
}

export interface IPoiClaimInfoList {
	/** id */
	id?: number
	/** poiId */
	poiId?: string
	/** 高德id */
	gaodeId?: string
	/** poi名称 */
	poiName?: string
	/** 省 */
	provinceName?: string
	/** 市 */
	cityName?: string
	/** 区 */
	districtName?: string
	/** 详细地址 */
	addressDetail?: string
	/** 认领状态 */
	claimStatus?: string
	/** 审核状态 */
	auditStatus?: string
	/** 法人资质提交状态 */
	legalPersonQualStatus?: string
	/** 资质信息 */
	qualificationMsg?: string
	/** 营业执照过期时间 */
	businessLicenseEndTime?: number
	/** 行业资质过期时间 */
	industryLicenseEndTime?: number
	/** banner */
	color?: string
	/** 门店ID */
	shopId?: string
}

export interface IData {
	/** 认领信息列表 */
	poiClaimInfoList?: IPoiClaimInfoList[]
	/** 数据总量 */
	total?: number
}

export interface IGetPoiClaimInfoPageResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getPoiClaimInfoPage(params: IGetPoiClaimInfoPagePayload, options = {}): Promise<IData> {
  return http.get('/api/edith/life_service/poi/poi_claim_info/page', { params, transform: false, ...{"needToast":true}, ...options })
}
