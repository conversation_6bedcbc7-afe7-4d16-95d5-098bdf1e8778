/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53035
  * @name: 物料中心-物料生成
  * @identifier: api.edith.material_center.material.generate.post
  * @version: undefined
  * @path: /api/edith/material_center/material/generate
  * @method: post
  * @description: 物料生成
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostMaterialGeneratePayload {
	/** 样式类型，1:设计样式，2:基础样式 */
	styleType: number
	/** 文案id */
	copytextId?: number
	/** 模版id */
	templateId?: number[]
	/** 店铺id列表 */
	shopIds: string[]
	/** 用户id */
	userId?: string
	/** 商户id */
	sellerId?: string
}

export interface IResponse {
	/** // */
	success: boolean
	/** // */
	msg?: string
	/** // */
	code?: number
	/** // */
	data?: string
	/** // */
	bizCode?: string
}

export interface IData {
	/** // */
	response: IResponse
	/** 任务id */
	taskId?: string
}

export interface IPostMaterialGenerateResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postMaterialGenerate(payload: IPostMaterialGeneratePayload, options = {}): Promise<IData> {
  return http.post('/api/edith/material_center/material/generate', payload, { transform: true, ...{"needToast":true}, ...options })
}
