/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53189
  * @name: 物料中心-批量下载物料
  * @identifier: api.edith.material_center.material_download.post
  * @version: undefined
  * @path: /api/edith/material_center/material_download
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostMaterialDownloadPayload {
	/** 物料id */
	materialIds?: string[]
}

export interface IResponse {
	/** 1 */
	success: boolean
	/** 1 */
	msg?: string
	/** 1 */
	code?: number
	/** 1 */
	data?: string
	/** 1 */
	bizCode?: string
}

export interface IData {
	/** 1 */
	response?: IResponse
	/** 1 */
	fileUrl?: string
}

export interface IPostMaterialDownloadResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postMaterialDownload(payload: IPostMaterialDownloadPayload, options = {}): Promise<IData> {
  return http.post('/api/edith/material_center/material_download', payload, { transform: true, ...{"needToast":true}, ...options })
}
