/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53032
  * @name: 物料中心-物料列表配置项
  * @identifier: api.edith.material_center.material_list.config.get
  * @version: undefined
  * @path: /api/edith/material_center/material_list/config
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IResponse {
	/** 1 */
	success: boolean
	/** 1 */
	msg?: string
	/** 1 */
	code?: number
	/** 1 */
	data?: string
	/** 1 */
	bizCode?: string
}

export interface IGroupList {
	/** 区域Id */
	groupId?: string
	/** 区域名称 */
	groupName?: string
}

export interface IData {
	/** 1 */
	response: IResponse
	/** 码类型列表 */
	codeTypeList?: number[]
	/** 样式类型列表 */
	styleList?: number[]
	/** 尺寸列表 */
	sizeList?: string[]
	/** 区域列表 */
	groupList?: IGroupList[]
}

export interface IGetMaterialListConfigResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMaterialListConfig(options = {}): Promise<IData> {
  return http.get('/api/edith/material_center/material_list/config', { transform: true, ...{"needToast":true}, ...options })
}
