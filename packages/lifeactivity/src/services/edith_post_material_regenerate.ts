/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53034
  * @name: 物料中心-物料重新生成
  * @identifier: api.edith.material_center.material.regenerate.post
  * @version: undefined
  * @path: /api/edith/material_center/material/regenerate
  * @method: post
  * @description: 物料重新生成
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostMaterialRegeneratePayload {
	/** 物料id */
	materialId: string
}

export interface IResponse {
	/** // */
	success: boolean
	/** // */
	msg?: string
	/** // */
	code?: number
	/** // */
	data?: string
	/** // */
	bizCode?: string
}

export interface IData {
	/** // */
	response?: IResponse
}

export interface IPostMaterialRegenerateResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postMaterialRegenerate(payload: IPostMaterialRegeneratePayload, options = {}): Promise<IData> {
	return http.post('/api/edith/material_center/material/regenerate', payload, { transform: true, ...{ "needToast": true }, ...options })
}
