/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 1355
  * @name: 长任务-提交任务
  * @identifier: api.edith.long_task.task.submit.post
  * @version: undefined
  * @path: /api/edith/long_task/task/submit
  * @method: post
  * @description: 提交任务
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IFileInfo {
	/** 1 */
	fileId?: string
	/** 1 */
	bizName?: string
	/** 1 */
	scene?: string
	/** 1 */
	cloudType?: number
	/** 1 */
	fileFormat?: string
}

export interface IInput {
	/** 1 */
	fileUrl?: string
	/** extra_json */
	extra?: string
	/** test */
	extraJson?: string
	/** file_info */
	fileInfo?: IFileInfo
}

export interface IPostTaskSubmitPayload {
	/** name */
	taskName: string
	/** 1 */
	input: IInput
	/** 1 */
	moduleName?: string
	/** 1 */
	subsystemAlias: string
}

export interface IData {
	/** id */
	taskId?: string
}

export interface IPostTaskSubmitResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postTaskSubmit(payload: IPostTaskSubmitPayload, options = {}): Promise<IData> {
  return http.post('/api/edith/long_task/task/submit', payload, { transform: true, ...{"needToast":true}, ...options })
}
