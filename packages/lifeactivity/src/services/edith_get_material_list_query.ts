/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 53033
  * @name: 物料中心-物料列表查询
  * @identifier: api.edith.material_center.material_list.query.get
  * @version: undefined
  * @path: /api/edith/material_center/material_list/query
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetMaterialListQueryPayload {
	/** 物料id */
	materialId?: string
	/** 码类型 1:门店二维码 */
	codeTypes?: number[]
	/** 样式类型 1:设计样式 2:基础样式 */
	styles?: number[]
	/** 尺寸 */
	sizes?: string[]
	/** 门店名称 */
	shopNames?: string[]
	/** 区域 */
	groups?: string[]
	/** 页码 */
	pageNo: number
	/** 页码大小 */
	pageSize: number
}

export interface IResponse {
	/** 1 */
	success: boolean
	/** 1 */
	msg?: string
	/** 1 */
	code?: number
	/** 1 */
	data?: string
	/** 1 */
	bizCode?: string
}

export interface IMaterialList {
	/** 物料id */
	materialId?: string
	/** 码类型 1:门店二维码 */
	codeType?: number
	/** 样式类型 1:设计样式 2:基础样式 */
	style?: number
	/** 尺寸 */
	size?: string
	/** 门店名称 */
	shopName?: string
	/** 区域 */
	group?: string
	/** 文案 */
	text?: string
	/** 状态 - 0-待处理,1-处理中,2-已完成,3-失败 */
	status?: number
	/** 图片url */
	imageUrl?: string
}

export interface IData {
	/** 1 */
	response: IResponse
	/** 物料列表 */
	materialList?: IMaterialList[]
	/** 总数 */
	total?: number
	/** 控制前端轮询 */
	isGenerating?: boolean
}

export interface IGetMaterialListQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMaterialListQuery(params: IGetMaterialListQueryPayload, options = {}): Promise<IData> {
  return http.get('/api/edith/material_center/material_list/query', { params, transform: true, ...{"needToast":true}, ...options })
}
