/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 13363
  * @name: 长任务-提交任务v2
  * @identifier: api.edith.long_task.task.submit.v2.post
  * @version: undefined
  * @path: /api/edith/long_task/task/submit/v2
  * @method: post
  * @description: 提交任务
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IFileInfo {
	/** 临时文件ID */
	fileId?: string
	/** cloud_type */
	cloudType?: number
	/** biz_name */
	bizName?: string
	/** scene */
	scene?: string
	/** file_format */
	fileFormat?: string
}

export interface IInput {
	/** 1 */
	fileUrl?: string
	/** extra_json */
	extra?: string
	/** test */
	extraJson?: string
	/** 文件信息 */
	fileInfo?: IFileInfo
}

export interface IPostSubmitV2Payload {
	/** name */
	taskName: string
	/** 1 */
	input: IInput
	/** 1 */
	moduleName?: string
	/** 1 */
	subsystemAlias: string
}

export interface IData {
	/** id */
	taskId?: string
}

export interface IPostSubmitV2Response {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postSubmitV2(payload: IPostSubmitV2Payload, options = {}): Promise<IData> {
  return http.post('/api/edith/long_task/task/submit/v2', payload, { transform: true, ...{"needToast":true}, ...options })
}
