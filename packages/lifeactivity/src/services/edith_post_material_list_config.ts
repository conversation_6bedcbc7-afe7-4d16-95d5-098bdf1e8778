/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 52724
  * @name: 物料中心-物料列表配置项
  * @identifier: api.life.material_center.material_list.config.post
  * @version: undefined
  * @path: /api/life/material_center/material_list/config
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostMaterialListConfigPayload {
	/** 商家id */
	sellerId?: string
}

export interface IResponse {
	/** success */
	success: boolean
	/** msg */
	msg?: string
	/** code */
	code?: number
	/** data */
	data?: string
	/** biz_code */
	bizCode?: string
}

export interface IData {
	/** response */
	response: IResponse
	/** 码类型列表 */
	codeTypeList?: number[]
	/** 样式类型列表 */
	styleList?: number[]
	/** 尺寸列表 */
	sizeList?: string[]
	/** 区域列表 */
	groupList?: string[]
}

export interface IPostMaterialListConfigResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postMaterialListConfig(payload: IPostMaterialListConfigPayload, options = {}): Promise<IData> {
  return http.post('/api/life/material_center/material_list/config', payload, { transform: true, ...{"needToast":true}, ...options })
}
