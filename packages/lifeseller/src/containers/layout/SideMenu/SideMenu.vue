<template>
  <Teleport to="#app-lifeseller">
    <div
      class="menu-wrapper"
      :bs="{ pointerEvents: canAction ? 'auto' : 'none' }"
      @mouseleave="handleResetHover"
    >
      <!-- 一级菜单 -->
      <div class="first-menu-wrapper">
        <div class="first-menu-content" :style="{ backgroundColor: '#F7F7F7' }">
          <div
            v-for="fItem in standardMenus"
            :id="`menu-${fItem.menuCode}`"
            :key="fItem.menuId"
            class="f-item-wrapper"
          >
            <template
              v-if="
                hoverMenuInfo.firstMenuName === fItem.firstMenuName &&
                  judgeShowCircle(fItem, fItem.menuLevel) &&
                  computedPopConfirmContent?.[fItem.menuCode]?.title &&
                  computedPopConfirmContent?.[fItem.menuCode]?.content
              "
            >
              <Popover placement="right-start" :offset="20">
                <div
                  :class="
                    selectMenuInfo.firstMenuId === fItem.menuId
                      ? 'f-item f-item-selected'
                      : 'f-item'
                  "
                  :style="{ color: genHovered(fItem) ? '#3A64FF' : '' }"
                  @click="handleSelectMenu(fItem, fItem.menuLevel)"
                  @mouseenter="handleFMouseenter(fItem)"
                  @mouseleave="handleFMouseleave(fItem)"
                >
                  <!--硬编码icon-->
                  <component
                    :is="ICON_MAP[fItem.menuCode] || ICON_MAP[fItem.firstMenuName] || ICON_MAP[fItem.menuName]"
                    :style="{ width: '22px', height: '22px' }"
                    :hovered="genHovered(fItem)"
                    :selected="selectMenuInfo.firstMenuName === fItem.firstMenuName"
                  />
                  <div class="f-item-name">{{ fItem.menuName }}</div>
                  <CircleIcon
                    v-if="judgeShowCircle(fItem, fItem.menuLevel)"
                    style="position: absolute; right: 0"
                  />
                </div>
                <template #popover>
                  <div class="custom-popover">
                    <div class="custom-popover-title">
                      {{ computedPopConfirmContent[fItem.menuCode]?.title }}
                    </div>
                    <div class="custom-popover-content">
                      {{ computedPopConfirmContent[fItem.menuCode]?.content }}
                    </div>
                    <div class="custom-arrow"></div>
                  </div>
                </template>
              </Popover>
            </template>
            <div
              v-else
              :class="
                selectMenuInfo.firstMenuId === fItem.menuId
                  ? 'f-item f-item-selected'
                  : 'f-item'
              "
              :style="{ color: genHovered(fItem) ? '#3A64FF' : '' }"
              @click="handleSelectMenu(fItem, fItem.menuLevel)"
              @mouseenter="handleFMouseenter(fItem)"
              @mouseleave="handleFMouseleave(fItem)"
            >
              <!--硬编码icon-->
              <component
                :is="ICON_MAP[fItem.menuCode] || ICON_MAP[fItem.firstMenuName]"
                :style="{ width: '22px', height: '22px' }"
                :hovered="genHovered(fItem)"
                :selected="selectMenuInfo.firstMenuName === fItem.firstMenuName"
              />
              <div class="f-item-name">{{ fItem.menuName }}</div>
              <CircleIcon
                v-if="judgeShowCircle(fItem, fItem.menuLevel)"
                style="position: absolute; right: 0"
              />
            </div>
          </div>
        </div>

        <CopyRight />
      </div>

      <div
        v-if="!!showFirstMenuName"
        :class="{
          'sub-menu-wrapper': true,
          'sub-menu-wrapper-hovered': hovered && !isHomePage,
          'sub-menu-wrapper-levitate': !selectMenuInfo.subMenus?.length,
        }"
      >
        <!-- 二级菜单 -->
        <div v-show="canExpand || hovered" class="sub-menu-content">
          <div class="select-first-menu-name">{{ showFirstMenuName }}</div>

          <div
            v-for="sItem in hovered && !isHomePage
              ? hoverMenuInfo.subMenus
              : selectMenuInfo.subMenus"
            :key="sItem.menuId"
            @click.stop="handleSelectMenu(sItem, sItem.menuLevel)"
          >
            <div class="s-item-wrapper" @click="handleToggle(sItem)">
              <Text
                ellipsis
                tooltip
                style="max-width: 85px"
                :class="
                  Number(selectMenuInfo.subMenuId) === Number(sItem.menuId)
                    ? 's-item-common s-item-selected'
                    : 's-item-common'
                "
              >
                {{ sItem.menuName }}
              </Text>
              <Icon
                v-if="sItem.subMenus && sItem.subMenus.length"
                size="small"
                :icon="Down"
                :class="sItem.toggle ? 'arrow-common arrow-toggle' : 'arrow-common'"
              />
              <CircleIcon
                v-if="
                  !(sItem.subMenus && sItem.subMenus.length) &&
                    judgeShowCircle(sItem, sItem.menuLevel)
                "
                :menu-name="sItem.menuName"
                style="margin-left: 3px"
              />
            </div>

            <!-- 三级菜单 -->
            <div v-show="sItem.toggle">
              <div
                v-for="tItem in sItem.subMenus"
                :key="tItem.menuId"
                :class="
                  Number(selectMenuInfo.subMenuId) === Number(tItem.menuId)
                    ? 't-item-common t-item-selected'
                    : 't-item-common'
                "
                @click.stop="handleSelectMenu(tItem, tItem.menuLevel)"
              >
                {{ tItem.menuName }}
                <CircleIcon
                  v-if="judgeShowCircle(tItem, tItem.menuLevel)"
                  :menu-name="tItem.menuName"
                  style="margin-left: 3px"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="tsx" setup>
  import {
    onMounted, computed, ref, watch, nextTick
  } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { UserInfo } from 'shared/seller/services/user'
  import { useStore } from 'vuex'
  import { Icon, Popover, Text } from '@xhs/delight'
  import { Down } from '@xhs/delight/icons'
  import useGuide from 'shared/seller/composables/use-guide'
  import useUser from 'shared/seller/composables/use-user'
  import { cloneDeep } from 'lodash'

  import { IMenu, getMenuListsWidthCache } from '~/services/menu'
  // import { getSupportMenus } from "~/utils/overwriteMenus/suportLoginMenus";
  import { MENU_LEVEL, ICON_MAP, POP_CONFIRM_CONTENT } from './config'
  import CircleIcon from './components/CircleIcon.vue'
  import CopyRight from './components/CopyRight.vue'
  import { LocalMenu, LocalSubMenu } from './type'
  import { handleMenuCrash, trackMenuModuleImpression, trackMenuClick } from './tracker'

  const props = defineProps<{ qkState: { [index: string]: any } }>()
  // const emit = defineEmits<{(e: 'onQKStateChange', value: {[index: string]: any}): void}>()
  const store = useStore()
  const router = useRouter()
  const route = useRoute()

  const { pageReady } = useGuide(true, true)
  const { businessType } = useUser()

  // 菜单出现功能指引，就不可以操作菜单区域
  const canAction = ref<boolean>(true)
  // 解决划过一级菜单立马切二级菜单的过度灵敏的问题
  const hoverSt = ref<number>()
  const hovered = ref(false)
  const canExpand = computed<boolean>(() => !!selectMenuInfo.value?.subMenus?.length)
  // 菜单数据
  const menuList = ref<IMenu[]>([])

  const computedPopConfirmContent = computed(() => {
    const popConfirmInfo = cloneDeep(POP_CONFIRM_CONTENT)
    for (const key in popConfirmInfo) {
      if (popConfirmInfo[key].visible && !popConfirmInfo[key].visible(businessType.value)) {
        delete popConfirmInfo[key]
      }
    }
    return popConfirmInfo
  })

  // 选中菜单的信息
  const selectMenuInfo = ref({
    firstMenuName: '',
    firstMenuId: 0,
    subMenuId: 0,
    subMenus: ([] as any) as LocalSubMenu[],
    menuLink: '',
  })

  // hover 菜单的信息
  const hoverMenuInfo = ref({
    firstMenuName: '',
    subMenus: [] as any,
  })

  // 是否是首页
  const isHomePage = computed(() => {
    if (hovered.value) {
      return hoverMenuInfo.value.firstMenuName === '商家首页'
    }
    return selectMenuInfo.value.firstMenuName === '商家首页'
  })
  const standardMenus = ref<LocalMenu[]>([])

  const {
    value: { sellerId },
  } = computed<UserInfo>(() => store.state?.Auth?.userInfo || {})
  const arkActiveMenu = computed(() => props.qkState.arkActiveMenu)

  const showFirstMenuName = computed(() => {
    if (hovered.value) {
      if (isHomePage.value && selectMenuInfo.value.firstMenuName !== '商家首页') {
        return selectMenuInfo.value.firstMenuName
      }

      if (isHomePage.value && selectMenuInfo.value.firstMenuName === '商家首页') {
        return ''
      }
      return hoverMenuInfo.value.firstMenuName
    }
    return selectMenuInfo.value.firstMenuName
  })

  const handleStandardMenus = () => {
    let count = 0 // 记录遍历次数
    const path = route.path
    const beforeSelectMenuInfo = { ...selectMenuInfo }

    selectMenuInfo.value = {
      firstMenuId: 0,
      subMenuId: 0,
      subMenus: [],
      firstMenuName: '',
      menuLink: '',
    }
    standardMenus.value = menuList.value
      .map(fItem => {
        if (fItem.subMenus) {
          fItem.subMenus = fItem.subMenus.map(sItem => {
            if (sItem.subMenus) {
              sItem.subMenus = sItem.subMenus.map(tItem => ({
                ...tItem,
                firstMenuId: fItem.menuId,
                firstMenuName: fItem.menuName,
                menuLevel: MENU_LEVEL.third,
                toggle: true,
              }))
            }
            return {
              ...sItem,
              firstMenuId: fItem.menuId,
              firstMenuName: fItem.menuName,
              menuLevel: MENU_LEVEL.second,
              toggle: true,
            }
          })
        }

        return {
          ...fItem,
          firstMenuId: fItem.menuId,
          firstMenuName: fItem.menuName,
          menuIcon: 'airport-slant',
          menuLevel: MENU_LEVEL.first,
        }
      })
      .map(fItem => {
        count += 1
        // 一级
        if (
          path === fItem.menuLink
          || (arkActiveMenu.value && arkActiveMenu.value === fItem.menuCode)
        ) {
          selectMenuInfo.value = {
            firstMenuId: fItem?.firstMenuId,
            subMenuId: 0,
            subMenus: [],
            firstMenuName: fItem.firstMenuName,
            menuLink: fItem.menuLink,
          }

          cacheMenuShowDot(
            fItem.menuName,
            computedPopConfirmContent.value?.[fItem.firstMenuName]?.key
          )
        }

        if (fItem?.subMenus?.length) {
          fItem.subMenus = fItem?.subMenus?.map(sItem => {
            if (sItem?.subMenus?.length) {
              sItem.subMenus = sItem.subMenus.map(tItem => {
                if (
                  path === tItem.menuLink
                  || (arkActiveMenu.value && arkActiveMenu.value === tItem.menuCode)
                ) {
                  // 三级
                  sItem.toggle = true

                  selectMenuInfo.value = {
                    firstMenuId: sItem.firstMenuId || 0,
                    subMenuId: tItem.menuId,
                    subMenus: (fItem?.subMenus as any) || [],
                    firstMenuName: fItem.firstMenuName,
                  } as any
                  cacheMenuShowDot(
                    tItem.menuName,
                    computedPopConfirmContent.value?.[tItem.menuName]?.key
                  )
                } else if (
                  count === menuList.value.length
                  && !selectMenuInfo.value.firstMenuName
                ) {
                  // 不刷新页面，未选中的时候默认取上一次的选中状态
                  sItem.toggle = true
                  selectMenuInfo.value = beforeSelectMenuInfo as any
                }

                return tItem
              })
            } else if (
              path === sItem.menuLink
              || (arkActiveMenu.value && arkActiveMenu.value === sItem.menuCode)
            ) {
              // 二级
              selectMenuInfo.value = {
                firstMenuId: (sItem as any)?.firstMenuId,
                subMenuId: String(sItem.menuId),
                subMenus: (fItem?.subMenus || []) as any,
                firstMenuName: fItem.firstMenuName,
              } as any
              cacheMenuShowDot(
                sItem.menuName,
                computedPopConfirmContent.value?.[sItem.menuName]?.key
              )
            } else if (
              count === menuList.value.length
              && !selectMenuInfo.value.firstMenuName
            ) {
              // 不刷新页面，未选中的时候默认取上一次的选中状态
              selectMenuInfo.value = beforeSelectMenuInfo as any
            }

            return sItem
          })
        }
        return fItem
      }) as any
  }

  onMounted(async () => {
    await fetchMenus()
    handleStandardMenus()
    await nextTick()
    pageReady()
  })

  watch(arkActiveMenu, () => {
    handleStandardMenus()
  })

  watch(
    () => route.path,
    () => {
      handleStandardMenus()
    },
    { immediate: true }
  )

  const cacheMenuShowDot = (menuName, key) => {
    localStorage.setItem(`${sellerId}-${menuName}-${key}-dotShowed`, 'showed')
  }
  const delCacheMenuShowDot = (menuName, key) => {
    localStorage.removeItem(`${sellerId}-${menuName}-${key}-dotShowed`)
  }
  const judgeCacheMenuShowDot = (menuName, key) =>
    // 不存在本地缓存
    localStorage.getItem(`${sellerId}-${menuName}-${key}-dotShowed`) !== 'showed'

  // const filterMenuByCode = (menuData, codes) => menuData.map(item => {
  //   if (item.subMenus) {
  //     // 如果当前项有子菜单，递归过滤子菜单
  //     item.subMenus = filterMenuByCode(item.subMenus, codes)
  //   }
  //   return codes.includes(item.menuCode) ? item : null
  // }).filter(item => item !== null)

  // const filterMenuList = (menuList: IMenu[]) => {
  //   const { roleList = [] } = store.state?.Auth?.userInfo || {}
  //   if (roleList?.length === 1 && roleList[0] === ERoleType.LIFE_ADMIN) {
  //     const listVal = filterMenuByCode(menuList, ['Order', 'VerificationList', 'VerificationByCode'])
  //     return listVal
  //   }
  //   return menuList
  // }

  const fetchMenus = async () => {
    try {
      const { menuList: val, errMsg }: any = await getMenuListsWidthCache(sellerId)
      menuList.value = val
      if (errMsg || !menuList.value?.length) {
        handleMenuCrash({
          error: errMsg || '商家后台菜单未返回',
          menuList,
        })
      }
      // 菜单曝光
      trackMenuModuleImpression()
    } catch (err: any) {
      handleMenuCrash({
        error: err?.message || '商家后台菜单接口崩溃',
        menuList: [],
      })
    }
  }

  const genHovered = fItem =>
    (fItem.hovered && selectMenuInfo.value.firstMenuName !== fItem.firstMenuName)
    || (hovered.value && hoverMenuInfo.value.firstMenuName === fItem.firstMenuName)

  const updateMenus = item => {
    standardMenus.value = standardMenus.value.map(fItem => {
      if (fItem.menuId === item.menuId) {
        return item
      }
      return fItem
    })
  }
  const handleResetHover = () => {
    if (hovered.value) {
      hoverMenuInfo.value.firstMenuName = ''
      hoverMenuInfo.value.subMenus = []
      hovered.value = false
    }
  }
  const handleFMouseenter = item => {
    hoverSt.value = (setTimeout(() => {
      hovered.value = true
      item.hovered = true
      updateMenus(item)
      handleHoveredMenu(item)
    }, 0) as any) as number
  }
  const handleFMouseleave = item => {
    clearTimeout(hoverSt.value)
    item.hovered = false
    updateMenus(item)
  }
  const handleJump = link => {
    if (!link) return
    if (/^https?/.test(link)) {
      window.open(link)
    } else {
      router.push({ path: link })
    }
  }
  const handleHoveredMenu = item => {
    hoverMenuInfo.value.firstMenuName = item.firstMenuName
    hoverMenuInfo.value.subMenus = item?.subMenus || []
  }
  // 点击【首页 下面没有二级与三级菜单】
  const handleSelectMenu = (item, level) => {
    let selectMenuLink = ''
    let selectSubMenuId = 0
    let showMenuInfo = { ...item }
    if (level === MENU_LEVEL.first) {
      handleResetHover()
      selectMenuInfo.value.firstMenuName = item.firstMenuName
      selectMenuInfo.value.firstMenuId = item.firstMenuId
      selectMenuInfo.value.subMenus = item?.subMenus || []

      if (!item?.subMenus?.length) {
        // 如果以一级菜单没有二级菜单
        selectMenuLink = item.menuLink
      } else if (item?.subMenus?.[0]?.subMenus?.length) {
        // 如果以一级菜单有三级菜单
        showMenuInfo = item?.subMenus?.[0]?.subMenus?.[0]
        selectSubMenuId = showMenuInfo?.menuId
        selectMenuLink = showMenuInfo?.menuLink
      } else {
        // 如果以一级菜单有二级级菜单
        showMenuInfo = item?.subMenus?.[0]
        selectSubMenuId = showMenuInfo?.menuId
        selectMenuLink = showMenuInfo?.menuLink
      }
    }

    if (level === MENU_LEVEL.second && !item?.subMenus?.length) {
      // 末级菜单
      handleResetHover()
      selectMenuInfo.value.firstMenuName = showMenuInfo.firstMenuName
      selectMenuInfo.value.firstMenuId = showMenuInfo.firstMenuId
      selectMenuLink = showMenuInfo.menuLink
      selectSubMenuId = showMenuInfo.menuId
    }

    if (level === MENU_LEVEL.third) {
      handleResetHover()
      selectMenuInfo.value.firstMenuName = showMenuInfo.firstMenuName
      selectMenuInfo.value.firstMenuId = showMenuInfo.firstMenuId
      selectMenuLink = showMenuInfo.menuLink
      selectSubMenuId = showMenuInfo.menuId
    }
    if (item?.showDot) {
      // 蓝点的点击上报
      // workerPopupClick({ eventName: showMenuInfo.menuName })
      cacheMenuShowDot(
        item?.menuName,
        computedPopConfirmContent.value?.[item.menuCode]?.key
      )
    } else {
      delCacheMenuShowDot(
        item?.menuName,
        computedPopConfirmContent.value?.[item.menuCode]?.key
      )
    }

    if (!/^https?/.test(selectMenuLink)) {
      // 若是跳转则不做激活
      selectMenuInfo.value.menuLink = selectMenuLink
      if (selectSubMenuId) {
        selectMenuInfo.value.subMenuId = selectSubMenuId
      }
    }
    handleJump(selectMenuLink)
    trackMenuClick(item)
  }

  // 二级菜单收缩/展开
  const handleToggle = item => {
    standardMenus.value = standardMenus.value.map(fItem => {
      if (fItem?.subMenus?.length) {
        fItem.subMenus = fItem.subMenus.map(sItem => {
          if (sItem.menuId === item.menuId) {
            sItem.toggle = !sItem.toggle
          }
          return sItem
        })
      }
      return fItem
    })
  }

  // 菜单蓝点显示
  const judgeShowCircle = (item, level) => {
    if (level === MENU_LEVEL.first) {
      // if (!item?.subMenus) {
      return (
        item.showDot
        && judgeCacheMenuShowDot(
          item.menuName,
          computedPopConfirmContent.value?.[item.menuCode]?.key
        )
      )
    // }
    // 只要存在二级【不存在三级】、三级有蓝点  那么一级就会显示蓝点
    // return item?.subMenus?.some(sMenu => sMenu?.subMenus?.some(tMenu => (tMenu.showDot && judgeCacheMenuShowDot(tMenu.menuName))) || (sMenu.showDot && !sMenu?.subMenus?.length && judgeCacheMenuShowDot(sMenu.menuName)))
    }

    if ([MENU_LEVEL.second, MENU_LEVEL.third].includes(level)) {
      // 不存在三级的二级  或者 三级
      return (
        item.showDot
        && judgeCacheMenuShowDot(
          item.menuName,
          computedPopConfirmContent.value?.[item.menuCode]?.key
        )
      )
    }
  }
</script>
<style lang="stylus" scoped>
.menu-wrapper{
  user-select none
  display inline-flex
  height calc(100vh - 48px)
  font-family "PingFang SC"
  position relative
  z-index 88
  overflow-y auto
}

  .first-menu-wrapper{
    display flex
    flex-direction column
    justify-content space-between
    border-right 1px solid rgba(51, 51, 51, 0.025)
  }

  .first-menu-content{
    flex 1
    // width 108px
    //background-color #F7F7F7
    display inline-flex
    flex-direction column
    padding 24px 11px 0 12px
    color rgba(51, 51, 51, 0.6)
    overflow-y scroll

    scrollbar-width none
    &::-webkit-scrollbar {
      display none
    }

    .f-item{
      display flex
      align-items center
      justify-content center
      cursor pointer
      padding 8px 14px

      &-wrapper{
        position relative
        display flex
        align-items center
        justify-content center

        &:not(:last-child){
          margin-bottom 10px
        }
      }

      &-name{
        font-family 'PingFang SC'
        margin-left 6px
        line-height 14px
      }

      &-hit{
        position absolute
        right -2px
      }

      &-selected{
        font-weight 500
        color #3A64FF
        background-color #ECF0FF
        border-radius 8px
        box-shadow -6px 0px 12px rgba(0, 0, 0, 0.01)
      }
    }
  }

  .sub-menu-wrapper{
    transition all 0.2s
    display inline-flex
    background-color: #F7F7F7

    margin: 0px 5px 0 0
    padding-top: 5px
    height: 100%
    &-hovered{
      box-shadow 3px 3px 6px rgba(0, 0, 0, 0.05)
      border-top-right-radius 12px
      border-bottom-right-radius 12px
    }
    &-levitate{
      // 悬浮侧边栏
      position absolute
      left 135px
      z-index 100
      box-shadow 3px 3px 6px rgba(0, 0, 0, 0.05)
      border-top-right-radius 12px
      border-bottom-right-radius 12px
    }

    .sub-menu-content{
      padding 30px 5px 0 12px
      overflow-y scroll
      width 105px

      scrollbar-width none
      &::-webkit-scrollbar {
        display none
      }
    }

    .select-first-menu-name{
      color #333333
      line-height 20px
      font-weight 500
      font-size 20px
      font-family 'PingFang SC'
      margin-bottom 30px
      white-space nowrap
    }

    .t-item{
      &-common{
        display flex
        align-items center
        margin-bottom 28px
        cursor pointer
        font-size 12px
        white-space nowrap
        line-height 12px
        color rgba(0, 0, 0, 0.52)

        &:is(:last-child){
          margin-bottom 28px
        }

        &:hover{
          color #3A64FF
        }
      }
      &-selected{
        font-weight 500
        color #3A64FF
      }
    }

    .arrow{
      &-common{
        fill: rgb(45, 45, 45);
        margin-left 5px
        transition transform 0.3s
      }
      &-toggle{
        transform rotate(180deg)
      }
    }

    .s-item{
      &-wrapper{
        display flex
        align-items center
        margin-bottom 32px
        cursor pointer
        &:hover{
          color #3A64FF
        }
      }

      &-common{
        white-space nowrap
        line-height 14px
      }
      &-selected{
        font-weight 500
        color #3A64FF
      }
    }
  }
  .custom-popover {
    width: 400px;
    background-color: rgba(60, 102, 255, 1);
    padding: 24px;
    border-radius: 8px;
    position: relative;
  }
  .custom-popover-title {
    color: rgba(255, 255, 255, 0.93);
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }
  .custom-popover-content {
    color: rgba(255, 255, 255, 0.93);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    margin: 20px 0;
  }
  .custom-arrow {
    width: 12px;
    height: 12px;
    background-color: rgba(60, 102, 255, 1);
    position: absolute;
    top: 12px;
    left: -6px;
    transform: rotate(45deg);
  }
</style>
