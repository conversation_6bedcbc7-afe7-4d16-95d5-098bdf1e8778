import { validateLifeHotelSeller } from 'shared/seller/composables/use-user'
import {
  HomeIcon,
  GoodIcon,
  BusinessIcon,
  StoreIcon,
  NoteIcon,
  PromotionIcon,
  LiveIcon,
  PromoteIcon,
  UserIcon,
  DataIcon,
  CustomerIcon,
  BalanceIcon,
  ServiceIcon,
  GoodThingIcon,
  ReturnIcon
} from './components/svgIcons'

export const MENU_LEVEL = {
  first: 1,
  second: 2,
  third: 3
}

export const ICON_MAP = {
  Home: HomeIcon, // 商家首页
  Shop: StoreIcon, // 店铺管理
  ItemManage: GoodIcon, // 商品管理
  LifeNote: NoteIcon, // 笔记经营
  Order: BusinessIcon, // 订单管理
  DataManage: DataIcon, // 数据中心
  Capital: BalanceIcon, // 财务管理
  Cooperation: ServiceIcon, // 合作管理
  Activity: PromotionIcon, // 平台活动
  Cs: CustomerIcon, // 客服管理
  ProviderMarket: BusinessIcon, // 服务商市场
  物料中心: BusinessIcon, // 物料市场

  // 以下是旧数据已废弃 from南冬
  首页: HomeIcon,
  商品: GoodIcon,
  交易: BusinessIcon, // TODO: 后期可以下掉
  订单: BusinessIcon,
  售后: ReturnIcon,
  店铺: StoreIcon,
  笔记: NoteIcon,
  带货: GoodThingIcon,
  营销: PromotionIcon,
  直播: LiveIcon,
  推广: PromoteIcon,
  用户: UserIcon,
  数据: DataIcon,
  客服: CustomerIcon,
  资金: BalanceIcon,
  服务: ServiceIcon,
  门店: StoreIcon,
  团购: GoodIcon,
  区域: ReturnIcon,
  商笔: NoteIcon,
  合作: ServiceIcon,
  账号: UserIcon
}

export const POP_CONFIRM_CONTENT = {
  Home: {
    key: '',
    title: '',
    content: '',
  }, // 商家首页
  Shop: {
    key: '',
    title: '',
    content: '',
  }, // 店铺管理
  ItemManage: {
    key: 'hotelMultiSku',
    title: '支持商家创建多规格商品啦',
    content: '可支持创建多规格属性商品（例如同线路不同出发地、同住宿商品不同套餐类型），高效率配置库存、价格等售卖信息',
  }, // 商品管理
  LifeNote: {
    key: '',
    title: '',
    content: '',
  }, // 笔记经营
  Order: {
    key: 'outsideRulesRefund',
    title: '支持商家规则外退款啦',
    content: '可在预约单发起协商退款，退款成功后可在【订单管理】-【退款单】查看退款记录与详情',
    visible: type => validateLifeHotelSeller(type)
  }, // 订单管理
  DataManage: {
    key: '',
    title: '',
    content: '',
  }, // 数据中心
  Capital: {
    key: '',
    title: '',
    content: '',
  }, // 财务管理
  Cooperation: {
    key: '',
    title: '',
    content: '',
  }, // 合作管理
  Activity: {
    key: '',
    title: '',
    content: '',
  }, // 平台活动
  Cs: {
    key: 'CustomerService',
    title: '客服能力支持配置啦',
    content: '支持配置欢迎语助手、自动回复等。',
  }, // 客服管理
}
