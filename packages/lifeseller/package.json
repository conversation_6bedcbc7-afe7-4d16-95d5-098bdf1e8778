{"name": "lifeseller", "version": "1.2.20", "description": "本地生活商家管理后台", "author": "luzhongkuan <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm.devops.xiaohongshu.com:7001/"}, "repository": {"type": "git", "url": "*******************************:fe/lifepartner.git"}, "scripts": {"dev": "formula dev -p 4000", "test": "echo \"Error: run tests from root\" && exit 1", "devtools": "modular start ./devtools.config.js"}, "private": true, "dependencies": {"qrcode": "^1.5.3", "@xhs/jarvis-3rd-loader": "^0.0.3"}}