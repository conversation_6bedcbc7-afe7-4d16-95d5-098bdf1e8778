import { Text } from '@xhs/delight'
import TableCellDesensitize, { UBA_LOG_MODULE_KEY_MAP, BizTypeEnum } from 'shared/components/TableCellDesensitize'
import TableColumn from '~/components/TableColumn/index.vue'
import { ToDetailFromEnum } from '~/constant/list'
import { getSecureUserPhone, getSecureUserPhoneByBook } from '~/services/sale'
import Link from '../../components/Link.vue'

export const schemeVacation = {
  filterScheme: [
    {
      name: 'orderTimeRange',
      label: '订单下单时间',
      column: 2,
      defaultValue: {},
      component: {
        is: 'DateRangePicker',
        props: {
          selectOnly: true,
          clearable: true,
          placeholder: { start: '开始日期', end: '结束日期' }
        },
      },
    },
    {
      name: 'orderId',
      label: '订单号',
      defaultValue: '',
      component: {
        props: {
          placeholder: '请输入小红书订单号',
        },
      },
    },
    {
      name: 'orderStatus',
      label: '订单状态',
      defaultValue: '',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择订单状态',
          options: [
            { label: '全部', value: 0 },
            { label: '待支付', value: 1 },
            { label: '待使用/已完成', value: 2 },
            { label: '已关闭', value: 3 },
            { label: '已取消', value: 4 },
          ],
        },
      },
    },
    {
      name: 'productName',
      label: '商品名称',
      defaultValue: '',
      component: {
        props: {
          placeholder: '请输入商品名称',
        },
      },
    },
  ],
  tableColumnScheme: [
    {
      field: 'orderId',
      title: '小红书订单号',
      dataIndex: 'orderId',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="orderId" />
    },
    {
      field: 'statusText',
      title: '订单状态',
      dataIndex: 'statusText',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="statusText" />
    },
    {
      field: 'createTime',
      title: '订单下单时间',
      dataIndex: 'createTime',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="createTime" />
    },
    {
      field: 'payTime',
      title: '订单支付时间',
      dataIndex: 'payTime',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="payTime" />
    },
    {
      field: 'skuId',
      title: '小红书商品ID',
      dataIndex: 'skuId',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="skuId" />
    },
    {
      field: 'productName',
      title: '商品名称',
      dataIndex: 'productName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="productName" />
    },
    {
      field: 'skuId',
      title: '规格ID',
      dataIndex: 'skuId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="skuId" />
    },
    {
      field: 'variantName',
      title: '商品规格',
      dataIndex: 'variantName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="variantName" />
    },
    {
      field: 'count',
      title: '券码数量',
      dataIndex: 'count',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="count" />
    },
    {
      field: 'originAmount',
      title: '原始金额',
      dataIndex: 'originAmount',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="originAmount" />
    },
    {
      field: 'discountAmount',
      title: '平台优惠',
      dataIndex: 'discountAmount',
      minWidth: 130,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="discountAmount" />
    },
    {
      field: 'payAmount',
      title: '实付金额',
      dataIndex: 'payAmount',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="payAmount" />
    },
    {
      field: 'userPhone',
      title: '下单用户手机号',
      dataIndex: 'userPhone',
      minWidth: 150,
      render: ({ rowData }) =>
        <TableCellDesensitize
          content={rowData.userPhone}
          color='text-paragraph'
          moduleKey={UBA_LOG_MODULE_KEY_MAP.local_get_secure_data_sale}
          payload={{
            biz_id: rowData.orderId,
            biz_type: BizTypeEnum.SALES_DETAIL,
            secure_fields: rowData.secureFields
          }}
          request={getSecureUserPhone(rowData)}
        />
    },
    {
      field: 'userName',
      title: '下单用户昵称',
      dataIndex: 'userName',
      minWidth: 130,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="userName" />
    }
  ],
}

export const schemeRerserve = {
  filterScheme: [
    {
      name: 'orderTimeRange',
      label: '订单下单时间',
      column: 2,
      defaultValue: {},
      component: {
        is: 'DateRangePicker',
        props: {
          selectOnly: true,
          clearable: true,
          placeholder: { start: '开始日期', end: '结束日期' }
        },
      },
    },
    {
      name: 'orderId',
      label: '订单号',
      defaultValue: '',
      component: {
        props: {
          placeholder: '请输入小红书订单号',
        },
      },
    },
    {
      name: 'orderStatus',
      label: '订单状态',
      defaultValue: '',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择订单状态',
          options: [
            { label: '全部', value: '' },
            { label: '待支付', value: '待支付' },
            { label: '待使用', value: '待使用' },
            { label: '待预约', value: '待预约' },
            { label: '待确认', value: '待确认' },
            { label: '已完成', value: '已完成' },
            { label: '已关闭', value: '已关闭' },
            { label: '已取消', value: '已取消' },
          ],
        },
      },
    },
  ],
  tableColumnScheme: [
    {
      field: 'orderId',
      title: '小红书订单号',
      dataIndex: 'orderId',
      minWidth: 116,
      render: ({ rowData }) => <Link rowData={rowData} text={rowData?.orderId} tag={ToDetailFromEnum.ORDER_ID}/>
    },
    {
      field: 'statusText',
      title: '订单状态',
      dataIndex: 'statusText',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="statusText" />
    },
    {
      field: 'createTime',
      title: '订单下单时间',
      dataIndex: 'createTime',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="createTime" />
    },
    {
      field: 'payTime',
      title: '订单支付时间',
      dataIndex: 'payTime',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="payTime" />
    },
    {
      field: 'productId',
      title: '小红书商品ID',
      dataIndex: 'productId',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="productId" />
    },
    {
      field: 'productName',
      title: '商品名称',
      dataIndex: 'productName',
      minWidth: 88,
      render: ({ rowData }) => <Text class="product-name" onClick={() => {
        window.location.href = `//${window.location.host}/app-item/create-item-itinerary/${rowData?.productId}`
      }}>{ rowData?.productName }</Text>
    },
    {
      field: 'skuId',
      title: '规格ID',
      dataIndex: 'skuId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="skuId" />
    },
    {
      field: 'variantName',
      title: '商品规格',
      dataIndex: 'variantName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="variantName" />
    },
    {
      field: 'count',
      title: '总份数',
      dataIndex: 'count',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="count" />
    },
    {
      field: 'bookCount',
      title: '预约份数',
      dataIndex: 'bookCount',
      minWidth: 100,
      render: ({ rowData }) => <Link rowData={rowData} text={rowData?.bookCount} tag={ToDetailFromEnum.RESERVE_COUNT}/>
    },
    {
      field: 'originAmount',
      title: '订单金额',
      dataIndex: 'originAmount',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="originAmount" />
    },
    {
      field: 'userPhone',
      title: '下单用户手机号',
      dataIndex: 'userPhone',
      minWidth: 130,
      render: ({ rowData }) =>
        <TableCellDesensitize
          content={rowData.userPhone}
          color='text-paragraph'
          moduleKey={UBA_LOG_MODULE_KEY_MAP.local_get_secure_data_sale_book}
          payload={{
            biz_id: rowData.orderId,
            biz_type: BizTypeEnum.SALES_DETAIL_BOOK_ORDER,
            secure_fields: rowData.secureFields
          }}
          request={getSecureUserPhoneByBook(rowData)}
        />
    },
    {
      field: 'userName',
      title: '下单用户昵称',
      dataIndex: 'userName',
      minWidth: 130,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="userName" />
    }
  ],
}

export const scheme = {
  schemeVacation,
  schemeRerserve
}
