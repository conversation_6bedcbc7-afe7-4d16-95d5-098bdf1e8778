import TableCellDesensitize, { UBA_LOG_MODULE_KEY_MAP, BizTypeEnum } from 'shared/components/TableCellDesensitize'
import TableColumn from '~/components/TableColumn/index.vue'
import { getSecureUserPhone } from '~/services/sale'

export const scheme = {
  filterScheme: [
    {
      name: 'orderTimeRange',
      label: '订单下单时间',
      column: 2,
      defaultValue: {},
      component: {
        is: 'DateRangePicker',
        props: {
          selectOnly: true,
          clearable: true,
          placeholder: { start: '开始日期', end: '结束日期' }
        },
      },
    },
    {
      name: 'orderId',
      label: '订单号',
      defaultValue: '',
      component: {
        props: {
          placeholder: '请输入小红书/三方订单号',
        },
      },
    },
    {
      name: 'orderStatus',
      label: '订单状态',
      defaultValue: '',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择订单状态',
          options: [
            { label: '全部', value: 0 },
            { label: '待支付', value: 1 },
            { label: '待使用/已完成', value: 2 },
            { label: '已关闭', value: 3 },
            { label: '已取消', value: 4 },
          ],
        },
      },
    },
    {
      name: 'orderMode',
      label: '订单券码类型',
      component: {
        is: 'Select',
        props: {
          placeholder: '请选择订单券码类型',
          options: [
            { label: '全部', value: '' },
            { label: '平台码', value: '1' },
            { label: '三方码', value: '2' },
          ],
        },
      },
    },
    {
      name: 'productName',
      label: '商品名称',
      defaultValue: '',
      component: {
        props: {
          placeholder: '请输入商品名称',
        },
      },
    },
  ],
  tableColumnScheme: [
    {
      field: 'orderId',
      title: '小红书订单号',
      dataIndex: 'orderId',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="orderId" />
    },
    {
      field: 'extOrderId',
      title: '三方订单号',
      dataIndex: 'extOrderId',
      minWidth: 102,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="extOrderId" />
    },
    {
      field: 'orderMode',
      title: '券码类型',
      dataIndex: 'orderMode',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="orderMode" />
    },
    {
      field: 'statusText',
      title: '订单状态',
      dataIndex: 'statusText',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="statusText" />
    },
    {
      field: 'createTime',
      title: '订单下单时间',
      dataIndex: 'createTime',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="createTime" />
    },
    {
      field: 'payTime',
      title: '订单支付时间',
      dataIndex: 'payTime',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="payTime" />
    },
    {
      field: 'skuId',
      title: '小红书商品ID',
      dataIndex: 'skuId',
      minWidth: 116,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="skuId" />
    },
    {
      field: 'extProductId',
      title: '三方商品ID',
      dataIndex: 'extProductId',
      minWidth: 102,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="extProductId" />
    },
    {
      field: 'productName',
      title: '商品名称',
      dataIndex: 'productName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="productName" />
    },
    {
      field: 'count',
      title: '券码数量',
      dataIndex: 'count',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="count" />
    },
    {
      field: 'originAmount',
      title: '原始金额',
      dataIndex: 'originAmount',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="originAmount" />
    },
    {
      field: 'discountAmount',
      title: '平台优惠',
      dataIndex: 'discountAmount',
      minWidth: 130,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="discountAmount" />
    },
    {
      field: 'payAmount',
      title: '实付金额',
      dataIndex: 'payAmount',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="payAmount" />
    },
    {
      field: 'userPhone',
      title: '下单用户手机号',
      dataIndex: 'userPhone',
      minWidth: 150,
      render: ({ rowData }) =>
        <TableCellDesensitize
          content={rowData.userPhone}
          color='text-paragraph'
          moduleKey={UBA_LOG_MODULE_KEY_MAP.local_get_secure_data_sale}
          payload={{
            biz_id: rowData.orderId,
            biz_type: BizTypeEnum.SALES_DETAIL,
            secure_fields: rowData.secureFields
          }}
          request={getSecureUserPhone(rowData)}
        />
    },
    {
      field: 'userName',
      title: '下单用户昵称',
      dataIndex: 'userName',
      minWidth: 130,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="userName" />
    }
  ],
}
